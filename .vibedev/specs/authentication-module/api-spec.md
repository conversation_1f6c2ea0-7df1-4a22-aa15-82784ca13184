# 认证模块API规格说明

## API概览

认证模块提供以下核心API端点，确保与Go版本完全兼容：

- **POST /account/login** - 用户登录
- **GET /account/profile** - 获取用户资料
- **POST /auth/request_access_token** - 请求API令牌
- **GET /sso/login/cloud** - SSO登录

## 认证方式

### 1. Bearer Token认证
```http
Authorization: Bearer <jwt_token>
```

### 2. API Token认证
```http
X-API-TOKEN: <api_token>
```

## API端点详细规格

### POST /account/login

**功能**: 用户密码登录，获取JWT访问令牌

**请求**:
```http
POST /account/login
Content-Type: application/json

{
  "password": "mypassword"
}
```

**请求参数**:
| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| password | string | 是 | 用户密码 |

**成功响应** (200 OK):
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "username": "coco-default-user",
  "id": "coco-default-user",
  "expire_in": 86400,
  "status": "ok"
}
```

**响应字段**:
| 字段 | 类型 | 描述 |
|------|------|------|
| access_token | string | JWT访问令牌 |
| username | string | 用户名 |
| id | string | 用户ID |
| expire_in | number | 令牌过期时间（秒） |
| status | string | 状态，固定为"ok" |

**错误响应**:
```json
// 401 Unauthorized - 密码错误
{
  "error": "Invalid credentials"
}

// 400 Bad Request - 请求格式错误
{
  "error": "Invalid request format"
}

// 500 Internal Server Error - 服务器错误
{
  "error": "Internal server error"
}
```

### GET /account/profile

**功能**: 获取当前认证用户的基本信息

**请求**:
```http
GET /account/profile
Authorization: Bearer <jwt_token>
# 或者
X-API-TOKEN: <api_token>
```

**成功响应** (200 OK):
```json
{
  "id": "coco-default-user",
  "username": "coco-default-user",
  "roles": [],
  "last_login": "2024-01-15T10:30:00Z"
}
```

**响应字段**:
| 字段 | 类型 | 描述 |
|------|------|------|
| id | string | 用户ID |
| username | string | 用户名 |
| roles | array | 用户角色列表 |
| last_login | string | 最后登录时间（ISO 8601格式） |

**错误响应**:
```json
// 401 Unauthorized - 认证失败
{
  "error": "Invalid authentication token"
}

// 403 Forbidden - 权限不足
{
  "error": "Permission denied"
}
```

### POST /auth/request_access_token

**功能**: 生成长期有效的API令牌

**请求**:
```http
POST /auth/request_access_token
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "my-api-token"
}
```

**请求参数**:
| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| name | string | 否 | 令牌名称，用于标识 |

**成功响应** (200 OK):
```json
{
  "access_token": "abc123def456...",
  "expire_in": 31536000
}
```

**响应字段**:
| 字段 | 类型 | 描述 |
|------|------|------|
| access_token | string | API访问令牌 |
| expire_in | number | 令牌过期时间（秒，365天） |

**错误响应**:
```json
// 401 Unauthorized - 认证失败
{
  "error": "Invalid authentication token"
}

// 500 Internal Server Error - 生成失败
{
  "error": "Failed to generate access token"
}
```

### GET /sso/login/cloud

**功能**: SSO单点登录，支持与Coco Cloud集成

**请求**:
```http
GET /sso/login/cloud?provider=coco-cloud&product=coco&request_id=12345
```

**查询参数**:
| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| provider | string | 否 | SSO提供商，默认"coco-cloud" |
| product | string | 否 | 产品标识，默认"coco" |
| request_id | string | 否 | 请求ID，用于追踪 |

**成功响应** (200 OK):
返回HTML页面，包含自动重定向到Coco App的逻辑：

```html
<!DOCTYPE html>
<html>
<head>
    <title>登录成功</title>
    <meta charset="UTF-8">
    <script>
        // 自动重定向到Coco App
        setTimeout(function() {
            window.location.href = "cocoai://oauth_callback?code=<jwt_token>&request_id=<request_id>&provider=<provider>&expire_in=<expire_in>";
        }, 1000);
    </script>
</head>
<body>
    <div>
        <h1>登录成功！</h1>
        <p>正在重定向到 Coco AI 应用...</p>
        <a href="cocoai://oauth_callback?code=<jwt_token>&request_id=<request_id>&provider=<provider>&expire_in=<expire_in>">
            如果未自动重定向，请点击这里
        </a>
    </div>
</body>
</html>
```

**重定向URL格式**:
```
cocoai://oauth_callback?code=<jwt_token>&request_id=<request_id>&provider=<provider>&expire_in=<expire_in>
```

**URL参数说明**:
| 参数 | 描述 |
|------|------|
| code | JWT访问令牌 |
| request_id | 原始请求ID |
| provider | SSO提供商 |
| expire_in | 令牌过期时间 |

## JWT令牌规格

### JWT Header
```json
{
  "alg": "HS256",
  "typ": "JWT"
}
```

### JWT Payload
```json
{
  "user_id": "coco-default-user",
  "username": "coco-default-user",
  "roles": [],
  "provider": "simple",
  "exp": **********,
  "iat": **********
}
```

### JWT Claims说明
| 字段 | 类型 | 描述 |
|------|------|------|
| user_id | string | 用户唯一标识 |
| username | string | 用户名 |
| roles | array | 用户角色列表 |
| provider | string | 认证提供商 |
| exp | number | 过期时间（Unix时间戳） |
| iat | number | 签发时间（Unix时间戳） |

## API令牌规格

### 令牌格式
```
<uuid>-<64位随机字符串>
```

示例：
```
550e8400-e29b-41d4-a716-************-abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890
```

### 令牌存储结构
```json
{
  "id": "token-uuid",
  "access_token": "actual-token-string",
  "user_id": "coco-default-user",
  "name": "my-api-token",
  "provider": "access_token",
  "token_type": "api",
  "roles": [],
  "permissions": [],
  "expire_in": **********,
  "created_at": "2024-01-15T10:30:00Z",
  "last_used": "2024-01-15T10:30:00Z",
  "is_active": true
}
```

## 错误代码规格

### HTTP状态码
| 状态码 | 描述 | 使用场景 |
|--------|------|----------|
| 200 | OK | 请求成功 |
| 400 | Bad Request | 请求格式错误 |
| 401 | Unauthorized | 认证失败 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 500 | Internal Server Error | 服务器内部错误 |

### 错误响应格式
```json
{
  "error": "错误类型",
  "message": "详细错误信息（可选）"
}
```

### 常见错误类型
| 错误类型 | 描述 |
|----------|------|
| Invalid credentials | 登录凭据无效 |
| Invalid authentication token | 认证令牌无效 |
| Token expired | 令牌已过期 |
| Missing authentication | 缺少认证信息 |
| Permission denied | 权限不足 |
| Invalid request format | 请求格式错误 |
| Internal server error | 服务器内部错误 |

## 安全要求

### 1. HTTPS支持
- 生产环境必须使用HTTPS
- 开发环境可使用HTTP

### 2. CORS配置
```http
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization, X-API-TOKEN
```

### 3. 安全头部
```http
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

### 4. 速率限制
- 登录端点：每分钟最多5次尝试
- API端点：每分钟最多100次请求

## 兼容性说明

### 与Go版本的兼容性
1. **API路径完全一致**
2. **请求/响应格式完全一致**
3. **JWT令牌格式兼容**
4. **错误响应格式一致**
5. **HTTP状态码一致**

### 客户端兼容性
- 支持现有Coco App客户端
- 支持现有API集成
- 无需客户端代码修改

## 性能指标

### 实际测试结果 (2025-01-03)

**JWT验证性能**:
- 首次验证: 4.57µs (远超目标 < 10ms)
- 缓存验证: 2.00µs
- 无效JWT验证: 986ns

**缓存性能**:
- 缓存命中: 821-839ns
- 缓存未命中: 1.0-1.03µs
- 缓存写入: 4.67-9.28µs

**并发性能**:
- 单线程: 11.2µs
- 10并发线程: 27.1µs
- 50并发线程: 77.5µs
- 100并发线程: 122.2µs

**系统指标**:
- 支持高并发访问
- 缓存命中率: > 95%
- 内存使用优化
- 零拷贝数据传输
