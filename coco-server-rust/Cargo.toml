[package]
name = "coco-server"
version = "0.1.0"
edition = "2021"

[dependencies]
# Web框架
axum = { version = "0.7", features = ["macros", "ws"] }
tower = { version = "0.4", features = ["util"] }
tower-http = { version = "0.5", features = ["cors", "trace", "timeout"] }

# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# 数据库
surrealdb = { version = "2.3.7", features = [
    "kv-rocksdb",
    "scripting",
    "protocol-ws",
] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"

# 错误处理
thiserror = "1.0"
anyhow = "1.0"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID
uuid = { version = "1.17.0", features = ["v4", "serde"] }

# 配置管理
config = "0.13"
toml = "0.8"
notify = "6.0"

# 验证
validator = { version = "0.16", features = ["derive"] }
url = "2.4"

# 其他依赖
bytes = "1.0"
futures = "0.3"
tokio-rustls = "0.24"
rustls-pemfile = "1.0"
reqwest = { version = "0.11", features = ["json"] }
bcrypt = "0.15"
jsonwebtoken = "9.3"
urlencoding = "2.1.3"
dashmap = "5.5"
regex = "1.10"
tokio-stream = "0.1"
async-trait = "0.1"
rand = "0.8"
sha2 = "0.10"

[dev-dependencies]
# 测试框架
tokio-test = "0.4"
mockall = "0.11"

# 性能测试
criterion = { version = "0.5", features = ["html_reports"] }

# 其他测试依赖
tokio-tungstenite = "0.20"
tempfile = "3.8"
http-body-util = "0.1"
hyper = { version = "0.14", features = ["full"] }

[[bench]]
name = "auth_performance"
harness = false
