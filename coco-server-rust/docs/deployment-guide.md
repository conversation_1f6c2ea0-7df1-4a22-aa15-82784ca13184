# Coco Server Rust 部署指南

## 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 10GB可用空间
- **网络**: 稳定的网络连接

### 软件要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+), macOS 10.15+, Windows 10+
- **Rust**: 1.70.0 或更高版本
- **SurrealDB**: 1.0.0 或更高版本
- **Git**: 用于代码获取

## 安装步骤

### 1. 安装Rust环境

```bash
# 安装Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# 验证安装
rustc --version
cargo --version
```

### 2. 安装SurrealDB

```bash
# macOS
brew install surrealdb/tap/surreal

# Linux
curl -sSf https://install.surrealdb.com | sh

# Windows (使用PowerShell)
iwr https://install.surrealdb.com -useb | iex
```

### 3. 获取源代码

```bash
git clone <repository-url>
cd coco-server-rust
```

### 4. 编译项目

```bash
# 开发模式编译
cargo build

# 生产模式编译（推荐）
cargo build --release
```

## 配置

### 1. 数据库配置

启动SurrealDB服务：

```bash
# 启动SurrealDB（使用RocksDB存储）
surreal start --user root --pass root rocksdb://./data/coco.db

# 或使用内存存储（仅用于测试）
surreal start --user root --pass root memory
```

### 2. 环境变量配置

创建 `.env` 文件：

```env
# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# 数据库配置
DATABASE_URL=ws://127.0.0.1:8000
DATABASE_USERNAME=root
DATABASE_PASSWORD=root
DATABASE_NAMESPACE=coco
DATABASE_NAME=coco

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRATION=3600

# 日志配置
RUST_LOG=info
LOG_LEVEL=info

# 缓存配置
CACHE_TTL=300
CACHE_MAX_SIZE=10000
```

### 3. 配置文件

项目使用TOML配置文件，主要配置文件位于 `config/` 目录：

- `config/default.toml`: 默认配置
- `config/development.toml`: 开发环境配置
- `config/production.toml`: 生产环境配置

## 运行服务

### 开发模式

```bash
# 直接运行
cargo run

# 或使用开发配置
RUST_ENV=development cargo run
```

### 生产模式

```bash
# 使用release版本
cargo run --release

# 或直接运行编译后的二进制文件
./target/release/coco-server
```

### 使用systemd（Linux）

创建systemd服务文件 `/etc/systemd/system/coco-server.service`：

```ini
[Unit]
Description=Coco Server Rust
After=network.target

[Service]
Type=simple
User=coco
WorkingDirectory=/opt/coco-server-rust
ExecStart=/opt/coco-server-rust/target/release/coco-server
Restart=always
RestartSec=5
Environment=RUST_ENV=production

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable coco-server
sudo systemctl start coco-server
sudo systemctl status coco-server
```

## 健康检查

### 基本健康检查

```bash
curl http://localhost:8080/health
```

预期响应：
```json
{
  "status": "healthy",
  "timestamp": "2025-01-03T10:00:00Z",
  "version": "0.1.0"
}
```

### 详细健康检查

```bash
curl http://localhost:8080/health/detailed
```

## 监控和日志

### 日志配置

日志级别可以通过环境变量控制：

```bash
# 设置日志级别
export RUST_LOG=debug  # trace, debug, info, warn, error

# 结构化日志输出
export LOG_FORMAT=json
```

### 监控指标

服务提供以下监控端点：

- `/health` - 基本健康检查
- `/metrics` - Prometheus格式指标（如果启用）
- `/info` - 服务信息

### 日志文件

默认日志输出到标准输出，可以重定向到文件：

```bash
./target/release/coco-server > /var/log/coco-server.log 2>&1
```

## 性能调优

### 1. 数据库连接池

在配置文件中调整连接池大小：

```toml
[database]
max_connections = 10
min_connections = 2
connection_timeout = 30
```

### 2. 缓存配置

```toml
[cache]
ttl = 300  # 5分钟
max_size = 10000
cleanup_interval = 60
```

### 3. 服务器配置

```toml
[server]
workers = 4  # 工作线程数
keep_alive = 75
client_timeout = 5000
```

## 安全配置

### 1. JWT密钥

确保使用强密钥：

```bash
# 生成随机密钥
openssl rand -base64 32
```

### 2. HTTPS配置

```toml
[tls]
enabled = true
cert_path = "/path/to/cert.pem"
key_path = "/path/to/key.pem"
```

### 3. 防火墙配置

```bash
# 只允许必要端口
sudo ufw allow 8080/tcp
sudo ufw enable
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查SurrealDB是否运行
   - 验证连接字符串和凭据

2. **端口被占用**
   - 检查端口使用情况：`netstat -tlnp | grep 8080`
   - 修改配置文件中的端口

3. **权限问题**
   - 确保运行用户有足够权限
   - 检查文件和目录权限

### 日志分析

```bash
# 查看错误日志
grep "ERROR" /var/log/coco-server.log

# 查看最近的日志
tail -f /var/log/coco-server.log

# 按时间过滤日志
journalctl -u coco-server --since "1 hour ago"
```

## 备份和恢复

### 数据备份

```bash
# 备份SurrealDB数据
surreal export --conn ws://localhost:8000 --user root --pass root --ns coco --db coco backup.sql
```

### 数据恢复

```bash
# 恢复数据
surreal import --conn ws://localhost:8000 --user root --pass root --ns coco --db coco backup.sql
```

## 升级指南

### 1. 停止服务

```bash
sudo systemctl stop coco-server
```

### 2. 备份数据

```bash
# 备份配置和数据
cp -r config/ config.backup/
# 备份数据库（见上面的备份步骤）
```

### 3. 更新代码

```bash
git pull origin main
cargo build --release
```

### 4. 重启服务

```bash
sudo systemctl start coco-server
sudo systemctl status coco-server
```

---

**文档版本**: 1.0  
**最后更新**: 2025-01-03  
**维护者**: Coco Server Team
