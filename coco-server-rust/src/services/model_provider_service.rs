use std::sync::Arc;

use async_trait::async_trait;
use tracing::{debug, info, warn};
use validator::Validate;

use crate::{
    error::error::CocoError,
    models::model_provider::*,
    repositories::model_provider_repo::{ModelProviderRepository, SearchQuery, SearchResponse},
    services::{cache_service::CacheService, validation_service::ValidationService},
};

/// 模型提供商业务服务
///
/// 实现模型提供商的核心业务逻辑，包括验证、缓存和业务规则
pub struct ModelProviderService {
    /// 数据访问层
    repository: Arc<dyn ModelProviderRepository>,
    /// 缓存服务
    cache_service: Arc<CacheService>,
    /// 验证服务
    validation_service: Arc<ValidationService>,
}

/// 模型提供商服务接口
///
/// 定义了模型提供商业务逻辑的抽象接口
#[async_trait]
pub trait ModelProviderServiceTrait: Send + Sync {
    /// 创建新的模型提供商
    async fn create(&self, req: CreateModelProviderRequest) -> Result<String, CocoError>;

    /// 根据ID获取模型提供商
    async fn get_by_id(&self, id: &str) -> Result<Option<ModelProvider>, CocoError>;

    /// 更新模型提供商
    async fn update(&self, id: &str, req: UpdateModelProviderRequest) -> Result<(), CocoError>;

    /// 删除模型提供商
    async fn delete(&self, id: &str) -> Result<(), CocoError>;

    /// 搜索模型提供商
    async fn search(&self, query: SearchQuery) -> Result<SearchResponse, CocoError>;

    /// 获取所有启用的模型提供商
    async fn get_enabled(&self) -> Result<Vec<ModelProvider>, CocoError>;

    /// 获取所有内置的模型提供商
    async fn get_builtin(&self) -> Result<Vec<ModelProvider>, CocoError>;
}

impl ModelProviderService {
    /// 创建新的模型提供商服务实例
    ///
    /// # 参数
    /// * `repository` - 模型提供商数据访问层
    /// * `cache_service` - 缓存服务
    /// * `validation_service` - 验证服务
    pub fn new(
        repository: Arc<dyn ModelProviderRepository>,
        cache_service: Arc<CacheService>,
        validation_service: Arc<ValidationService>,
    ) -> Self {
        Self {
            repository,
            cache_service,
            validation_service,
        }
    }

    /// 验证内置提供商保护逻辑
    ///
    /// # 参数
    /// * `provider` - 要检查的模型提供商
    /// * `operation` - 操作类型（用于错误消息）
    async fn validate_builtin_protection(
        &self,
        provider: &ModelProvider,
        operation: &str,
    ) -> Result<(), CocoError> {
        if provider.builtin {
            match operation {
                "delete" => {
                    return Err(CocoError::builtin_provider_protection(
                        "内置模型提供商不能被删除",
                    ));
                }
                "update_name" => {
                    return Err(CocoError::builtin_provider_protection(
                        "内置模型提供商的名称不能被修改",
                    ));
                }
                "update_builtin" => {
                    return Err(CocoError::builtin_provider_protection(
                        "内置模型提供商的builtin字段不能被修改",
                    ));
                }
                "update_created" => {
                    return Err(CocoError::builtin_provider_protection(
                        "内置模型提供商的created字段不能被修改",
                    ));
                }
                _ => {}
            }
        }
        Ok(())
    }

    /// 检查名称唯一性
    ///
    /// # 参数
    /// * `name` - 要检查的名称
    /// * `exclude_id` - 要排除的ID（用于更新时检查）
    async fn check_name_uniqueness(
        &self,
        name: &str,
        exclude_id: Option<&str>,
    ) -> Result<(), CocoError> {
        if let Some(existing) = self.repository.find_by_name(name).await? {
            if let Some(exclude_id) = exclude_id {
                if existing.id != exclude_id {
                    return Err(CocoError::model_provider_validation(&format!(
                        "模型提供商名称 '{}' 已被其他提供商使用",
                        name
                    )));
                }
            } else {
                return Err(CocoError::model_provider_validation(&format!(
                    "模型提供商名称 '{}' 已存在",
                    name
                )));
            }
        }
        Ok(())
    }

    /// 验证模型提供商创建请求
    ///
    /// # 参数
    /// * `req` - 创建请求
    fn validate_create_request(&self, req: &CreateModelProviderRequest) -> Result<(), CocoError> {
        // 使用validator进行基础验证
        req.validate()
            .map_err(|e| CocoError::model_provider_validation(&format!("请求验证失败: {}", e)))?;

        // 验证API类型
        self.validate_api_type(&req.api_type)?;

        Ok(())
    }

    /// 验证模型提供商更新请求
    ///
    /// # 参数
    /// * `req` - 更新请求
    fn validate_update_request(&self, req: &UpdateModelProviderRequest) -> Result<(), CocoError> {
        // 使用validator进行基础验证
        req.validate()
            .map_err(|e| CocoError::model_provider_validation(&format!("请求验证失败: {}", e)))?;

        // 验证API类型（如果提供）
        if let Some(api_type) = &req.api_type {
            self.validate_api_type(api_type)?;
        }

        Ok(())
    }

    /// 验证API类型
    ///
    /// # 参数
    /// * `api_type` - API类型
    fn validate_api_type(&self, api_type: &str) -> Result<(), CocoError> {
        let valid_types = [
            "openai",
            "azure",
            "anthropic",
            "google",
            "cohere",
            "huggingface",
            "ollama",
            "custom",
        ];

        if !valid_types.contains(&api_type) {
            return Err(CocoError::model_provider_validation(&format!(
                "不支持的API类型: {}，支持的类型: {}",
                api_type,
                valid_types.join(", ")
            )));
        }

        Ok(())
    }

    /// 构建缓存键
    ///
    /// # 参数
    /// * `id` - 模型提供商ID
    fn build_cache_key(&self, id: &str) -> String {
        format!("model_provider:{}", id)
    }

    /// 失效缓存
    ///
    /// # 参数
    /// * `id` - 模型提供商ID
    async fn invalidate_cache(&self, id: &str) {
        let cache_key = self.build_cache_key(id);
        self.cache_service.remove_cache(&cache_key);
        debug!("已失效模型提供商缓存: {}", cache_key);
    }

    /// 从缓存获取模型提供商
    ///
    /// # 参数
    /// * `id` - 模型提供商ID
    async fn get_from_cache(&self, id: &str) -> Option<ModelProvider> {
        let cache_key = self.build_cache_key(id);
        if let Some(cached_json) = self.cache_service.get_cache(&cache_key) {
            match serde_json::from_str::<ModelProvider>(&cached_json) {
                Ok(provider) => {
                    debug!("从缓存获取模型提供商: {}", id);
                    return Some(provider);
                }
                Err(e) => {
                    warn!("反序列化缓存的模型提供商失败: {}, 删除缓存", e);
                    self.cache_service.remove_cache(&cache_key);
                }
            }
        }
        None
    }

    /// 缓存模型提供商
    ///
    /// # 参数
    /// * `provider` - 要缓存的模型提供商
    async fn cache_provider(&self, provider: &ModelProvider) {
        let cache_key = self.build_cache_key(&provider.id);
        match serde_json::to_string(provider) {
            Ok(json) => {
                // 30分钟TTL
                if let Err(e) = self
                    .cache_service
                    .set_cache(cache_key.clone(), json, Some(1800))
                {
                    warn!("缓存模型提供商失败: {}", e);
                } else {
                    debug!("已缓存模型提供商: {}", cache_key);
                }
            }
            Err(e) => {
                warn!("序列化模型提供商失败: {}", e);
            }
        }
    }
}

#[async_trait]
impl ModelProviderServiceTrait for ModelProviderService {
    /// 创建新的模型提供商
    async fn create(&self, req: CreateModelProviderRequest) -> Result<String, CocoError> {
        info!("开始创建模型提供商: {}", req.name);

        // 验证请求
        self.validate_create_request(&req)?;

        // 检查名称唯一性
        self.check_name_uniqueness(&req.name, None).await?;

        // 创建模型提供商实例
        let provider = ModelProvider::new(req);

        // 保存到数据库
        let created_id = self.repository.create(&provider).await?;

        // 缓存新创建的提供商
        if let Ok(Some(created_provider)) = self.repository.get_by_id(&created_id).await {
            self.cache_provider(&created_provider).await;
        }

        info!("模型提供商创建成功: {}", created_id);
        Ok(created_id)
    }

    /// 根据ID获取模型提供商
    async fn get_by_id(&self, id: &str) -> Result<Option<ModelProvider>, CocoError> {
        debug!("获取模型提供商: {}", id);

        // 先尝试从缓存获取
        if let Some(cached_provider) = self.get_from_cache(id).await {
            debug!("从缓存返回模型提供商: {}", id);
            return Ok(Some(cached_provider));
        }

        // 从数据库获取
        let provider = self.repository.get_by_id(id).await?;

        // 如果找到，缓存它
        if let Some(ref provider) = provider {
            self.cache_provider(provider).await;
        }

        Ok(provider)
    }

    /// 更新模型提供商
    async fn update(&self, id: &str, req: UpdateModelProviderRequest) -> Result<(), CocoError> {
        info!("开始更新模型提供商: {}", id);

        // 验证请求
        self.validate_update_request(&req)?;

        // 获取现有的提供商
        let mut provider = self
            .repository
            .get_by_id(id)
            .await?
            .ok_or_else(|| CocoError::model_provider_not_found(id))?;

        // 检查内置提供商保护
        if req.name.is_some() && req.name.as_ref().unwrap() != &provider.name {
            self.validate_builtin_protection(&provider, "update_name")
                .await?;
        }

        // 检查名称唯一性（如果名称发生变化）
        if let Some(ref new_name) = req.name {
            if new_name != &provider.name {
                self.check_name_uniqueness(new_name, Some(id)).await?;
            }
        }

        // 更新提供商
        provider.update(req);

        // 保存到数据库
        self.repository.update(&provider).await?;

        // 失效缓存
        self.invalidate_cache(id).await;

        info!("模型提供商更新成功: {}", id);
        Ok(())
    }

    /// 删除模型提供商
    async fn delete(&self, id: &str) -> Result<(), CocoError> {
        info!("开始删除模型提供商: {}", id);

        // 获取现有的提供商
        let provider = self
            .repository
            .get_by_id(id)
            .await?
            .ok_or_else(|| CocoError::model_provider_not_found(id))?;

        // 检查内置提供商保护
        self.validate_builtin_protection(&provider, "delete")
            .await?;

        // 从数据库删除
        self.repository.delete(id).await?;

        // 失效缓存
        self.invalidate_cache(id).await;

        info!("模型提供商删除成功: {}", id);
        Ok(())
    }

    /// 搜索模型提供商
    async fn search(&self, query: SearchQuery) -> Result<SearchResponse, CocoError> {
        debug!("搜索模型提供商: {:?}", query);
        self.repository.search(&query).await
    }

    /// 获取所有启用的模型提供商
    async fn get_enabled(&self) -> Result<Vec<ModelProvider>, CocoError> {
        debug!("获取所有启用的模型提供商");
        self.repository.get_enabled().await
    }

    /// 获取所有内置的模型提供商
    async fn get_builtin(&self) -> Result<Vec<ModelProvider>, CocoError> {
        debug!("获取所有内置的模型提供商");
        self.repository.get_builtin().await
    }
}

#[cfg(test)]
mod tests {
    use std::{collections::HashMap, sync::Mutex};

    use async_trait::async_trait;
    use chrono::Utc;

    use super::*;
    use crate::repositories::model_provider_repo::SearchQuery;

    /// Mock ModelProviderRepository for testing
    struct MockModelProviderRepository {
        providers: Arc<Mutex<HashMap<String, ModelProvider>>>,
        next_id: Arc<Mutex<u32>>,
    }

    impl MockModelProviderRepository {
        fn new() -> Self {
            Self {
                providers: Arc::new(Mutex::new(HashMap::new())),
                next_id: Arc::new(Mutex::new(1)),
            }
        }

        fn add_provider(&self, mut provider: ModelProvider) {
            let mut providers = self.providers.lock().unwrap();
            if provider.id.is_empty() {
                let mut next_id = self.next_id.lock().unwrap();
                provider.id = format!("test-{}", *next_id);
                *next_id += 1;
            }
            providers.insert(provider.id.clone(), provider);
        }
    }

    #[async_trait]
    impl ModelProviderRepository for MockModelProviderRepository {
        async fn create(&self, provider: &ModelProvider) -> Result<String, CocoError> {
            let mut providers = self.providers.lock().unwrap();
            let mut next_id = self.next_id.lock().unwrap();
            let id = format!("test-{}", *next_id);
            *next_id += 1;

            let mut new_provider = provider.clone();
            new_provider.id = id.clone();
            providers.insert(id.clone(), new_provider);
            Ok(id)
        }

        async fn get_by_id(&self, id: &str) -> Result<Option<ModelProvider>, CocoError> {
            let providers = self.providers.lock().unwrap();
            Ok(providers.get(id).cloned())
        }

        async fn update(&self, provider: &ModelProvider) -> Result<(), CocoError> {
            let mut providers = self.providers.lock().unwrap();
            if providers.contains_key(&provider.id) {
                providers.insert(provider.id.clone(), provider.clone());
                Ok(())
            } else {
                Err(CocoError::model_provider_not_found(&provider.id))
            }
        }

        async fn delete(&self, id: &str) -> Result<(), CocoError> {
            let mut providers = self.providers.lock().unwrap();
            if providers.remove(id).is_some() {
                Ok(())
            } else {
                Err(CocoError::model_provider_not_found(id))
            }
        }

        async fn search(&self, _query: &SearchQuery) -> Result<SearchResponse, CocoError> {
            let providers = self.providers.lock().unwrap();
            let all_providers: Vec<ModelProvider> = providers.values().cloned().collect();
            Ok(SearchResponse::new(all_providers, providers.len(), 10))
        }

        async fn exists(&self, id: &str) -> Result<bool, CocoError> {
            let providers = self.providers.lock().unwrap();
            Ok(providers.contains_key(id))
        }

        async fn find_by_name(&self, name: &str) -> Result<Option<ModelProvider>, CocoError> {
            let providers = self.providers.lock().unwrap();
            Ok(providers.values().find(|p| p.name == name).cloned())
        }

        async fn get_enabled(&self) -> Result<Vec<ModelProvider>, CocoError> {
            let providers = self.providers.lock().unwrap();
            Ok(providers.values().filter(|p| p.enabled).cloned().collect())
        }

        async fn get_builtin(&self) -> Result<Vec<ModelProvider>, CocoError> {
            let providers = self.providers.lock().unwrap();
            Ok(providers.values().filter(|p| p.builtin).cloned().collect())
        }
    }

    fn create_test_service() -> ModelProviderService {
        let repository = Arc::new(MockModelProviderRepository::new());
        let cache_manager = Arc::new(crate::repositories::cache_manager::CacheManager::new(1800));
        let cache_service = Arc::new(CacheService::new(cache_manager));
        let validation_service = Arc::new(ValidationService::new());

        ModelProviderService::new(repository, cache_service, validation_service)
    }

    fn create_test_request() -> CreateModelProviderRequest {
        CreateModelProviderRequest {
            name: "Test Provider".to_string(),
            api_key: "test-api-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.openai.com".to_string(),
            icon: "test-icon".to_string(),
            models: vec![],
            enabled: true,
            description: "Test description".to_string(),
        }
    }

    fn create_builtin_provider() -> ModelProvider {
        ModelProvider {
            id: "builtin-1".to_string(),
            created: Utc::now(),
            updated: Utc::now(),
            name: "Builtin Provider".to_string(),
            api_key: "builtin-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.openai.com".to_string(),
            icon: "builtin-icon".to_string(),
            models: vec![],
            enabled: true,
            builtin: true,
            description: "Builtin provider".to_string(),
        }
    }

    #[tokio::test]
    async fn test_create_provider_success() {
        let service = create_test_service();
        let request = create_test_request();

        let result = service.create(request).await;
        assert!(result.is_ok());

        let created_id = result.unwrap();
        assert!(!created_id.is_empty());

        // 验证创建的提供商
        let provider = service.get_by_id(&created_id).await.unwrap();
        assert!(provider.is_some());
        let provider = provider.unwrap();
        assert_eq!(provider.name, "Test Provider");
        assert_eq!(provider.api_type, "openai");
        assert!(!provider.builtin);
    }

    #[tokio::test]
    async fn test_create_provider_duplicate_name() {
        let service = create_test_service();
        let request = create_test_request();

        // 第一次创建应该成功
        let result1 = service.create(request.clone()).await;
        assert!(result1.is_ok());

        // 第二次创建相同名称应该失败
        let result2 = service.create(request).await;
        assert!(result2.is_err());
        assert!(result2.unwrap_err().to_string().contains("已存在"));
    }

    #[tokio::test]
    async fn test_create_provider_invalid_api_type() {
        let service = create_test_service();
        let mut request = create_test_request();
        request.api_type = "invalid-type".to_string();

        let result = service.create(request).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("不支持的API类型"));
    }

    #[tokio::test]
    async fn test_get_provider_by_id() {
        let service = create_test_service();
        let request = create_test_request();

        // 创建提供商
        let created_id = service.create(request).await.unwrap();

        // 获取提供商
        let provider = service.get_by_id(&created_id).await.unwrap();
        assert!(provider.is_some());
        let provider = provider.unwrap();
        assert_eq!(provider.id, created_id);
        assert_eq!(provider.name, "Test Provider");
    }

    #[tokio::test]
    async fn test_get_provider_not_found() {
        let service = create_test_service();

        let result = service.get_by_id("non-existent").await.unwrap();
        assert!(result.is_none());
    }

    #[tokio::test]
    async fn test_update_provider_success() {
        let service = create_test_service();
        let request = create_test_request();

        // 创建提供商
        let created_id = service.create(request).await.unwrap();

        // 更新提供商
        let update_request = UpdateModelProviderRequest {
            name: Some("Updated Provider".to_string()),
            api_key: None,
            api_type: None,
            base_url: None,
            icon: None,
            models: None,
            enabled: Some(false),
            description: Some("Updated description".to_string()),
        };

        let result = service.update(&created_id, update_request).await;
        assert!(result.is_ok());

        // 验证更新
        let provider = service.get_by_id(&created_id).await.unwrap().unwrap();
        assert_eq!(provider.name, "Updated Provider");
        assert!(!provider.enabled);
        assert_eq!(provider.description, "Updated description");
    }

    #[tokio::test]
    async fn test_update_provider_not_found() {
        let service = create_test_service();

        let update_request = UpdateModelProviderRequest {
            name: Some("Updated Provider".to_string()),
            api_key: None,
            api_type: None,
            base_url: None,
            icon: None,
            models: None,
            enabled: None,
            description: None,
        };

        let result = service.update("non-existent", update_request).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_delete_provider_success() {
        let service = create_test_service();
        let request = create_test_request();

        // 创建提供商
        let created_id = service.create(request).await.unwrap();

        // 删除提供商
        let result = service.delete(&created_id).await;
        assert!(result.is_ok());

        // 验证删除
        let provider = service.get_by_id(&created_id).await.unwrap();
        assert!(provider.is_none());
    }

    #[tokio::test]
    async fn test_delete_builtin_provider_protection() {
        let mock_repo = Arc::new(MockModelProviderRepository::new());
        let builtin_provider = create_builtin_provider();

        // 手动添加内置提供商到mock repository
        mock_repo.add_provider(builtin_provider.clone());

        let cache_manager = Arc::new(crate::repositories::cache_manager::CacheManager::new(1800));
        let cache_service = Arc::new(CacheService::new(cache_manager));
        let validation_service = Arc::new(ValidationService::new());

        let service = ModelProviderService::new(mock_repo, cache_service, validation_service);

        // 尝试删除内置提供商应该失败
        let result = service.delete(&builtin_provider.id).await;
        assert!(result.is_err());
        assert!(result
            .unwrap_err()
            .to_string()
            .contains("内置模型提供商不能被删除"));
    }

    #[tokio::test]
    async fn test_validate_api_type() {
        let service = create_test_service();

        // 测试有效的API类型
        assert!(service.validate_api_type("openai").is_ok());
        assert!(service.validate_api_type("azure").is_ok());
        assert!(service.validate_api_type("anthropic").is_ok());

        // 测试无效的API类型
        assert!(service.validate_api_type("invalid").is_err());
        assert!(service.validate_api_type("").is_err());
    }

    #[tokio::test]
    async fn test_cache_functionality() {
        let service = create_test_service();
        let request = create_test_request();

        // 创建提供商
        let created_id = service.create(request).await.unwrap();

        // 第一次获取（从数据库）
        let provider1 = service.get_by_id(&created_id).await.unwrap().unwrap();

        // 第二次获取（应该从缓存）
        let provider2 = service.get_by_id(&created_id).await.unwrap().unwrap();

        assert_eq!(provider1.id, provider2.id);
        assert_eq!(provider1.name, provider2.name);
    }
}
