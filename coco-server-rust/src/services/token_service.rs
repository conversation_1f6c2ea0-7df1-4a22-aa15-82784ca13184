use crate::error::error::CocoError;
use crate::models::access_token::{generate_api_token_name, AccessToken};
use crate::repositories::token_repository::TokenRepository;
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{debug, error, info};

/// API令牌管理器 - TASK-007要求的TokenManager结构体
/// 这是TokenService的别名，提供相同的功能
pub type TokenManager = TokenService;

/// API令牌管理服务
#[derive(Clone)]
pub struct TokenService {
    /// 令牌数据访问层
    repository: Arc<TokenRepository>,
}

impl TokenService {
    /// 创建新的令牌服务实例
    pub fn new(repository: Arc<TokenRepository>) -> Self {
        Self { repository }
    }

    /// 生成新的API令牌
    pub async fn generate_api_token(
        &self,
        user_id: &str,
        name: Option<String>,
    ) -> Result<AccessToken, CocoError> {
        let token_name = name.unwrap_or_else(|| generate_api_token_name(None));
        let token = AccessToken::new(user_id.to_string(), token_name);

        info!(
            "Generating API token for user: {}, token_id: {}, name: {}",
            user_id, token.id, token.name
        );

        // 存储令牌到数据库
        self.repository
            .create_token(&token)
            .await
            .map_err(|e| CocoError::server(&format!("Failed to create token: {}", e)))?;

        debug!("API token generated successfully: {}", token.id);
        Ok(token)
    }

    /// 验证API令牌
    pub async fn validate_api_token(&self, token: &str) -> Result<AccessToken, CocoError> {
        match self.repository.find_by_token(token).await {
            Ok(Some(access_token)) => {
                if !access_token.is_active {
                    error!("Token is inactive: {}", access_token.id);
                    return Err(CocoError::unauthorized("Token is inactive"));
                }

                if access_token.is_expired() {
                    error!("Token is expired: {}", access_token.id);
                    return Err(CocoError::unauthorized("Token is expired"));
                }

                debug!("Token validation successful: {}", access_token.id);
                Ok(access_token)
            }
            Ok(None) => {
                debug!("Token not found: {}", token);
                Err(CocoError::unauthorized("Invalid token"))
            }
            Err(e) => {
                error!("Database error during token validation: {}", e);
                Err(CocoError::server("Token validation failed"))
            }
        }
    }

    /// 撤销令牌
    pub async fn revoke_token(&self, token_id: &str, user_id: &str) -> Result<(), CocoError> {
        info!("Revoking token: {} for user: {}", token_id, user_id);

        // 验证令牌属于指定用户
        match self.repository.find_by_id(token_id).await {
            Ok(Some(token)) => {
                if token.user_id != user_id {
                    error!(
                        "Permission denied: token {} does not belong to user {}",
                        token_id, user_id
                    );
                    return Err(CocoError::not_found("Token not found or permission denied"));
                }

                // 撤销令牌
                self.repository
                    .revoke_token(token_id)
                    .await
                    .map_err(|e| CocoError::server(&format!("Failed to revoke token: {}", e)))?;

                info!("Token revoked successfully: {}", token_id);
                Ok(())
            }
            Ok(None) => {
                error!("Token not found: {}", token_id);
                Err(CocoError::not_found("Token not found"))
            }
            Err(e) => {
                error!("Database error during token revocation: {}", e);
                Err(CocoError::server("Token revocation failed"))
            }
        }
    }

    /// 获取用户的所有令牌
    pub async fn get_user_tokens(&self, user_id: &str) -> Result<Vec<AccessToken>, CocoError> {
        debug!("Getting tokens for user: {}", user_id);

        match self.repository.find_by_user_id(user_id).await {
            Ok(tokens) => {
                debug!("Found {} tokens for user: {}", tokens.len(), user_id);
                Ok(tokens)
            }
            Err(e) => {
                error!("Database error while getting user tokens: {}", e);
                Err(CocoError::server("Failed to get user tokens"))
            }
        }
    }

    /// 重命名令牌
    pub async fn rename_token(
        &self,
        token_id: &str,
        user_id: &str,
        new_name: String,
    ) -> Result<(), CocoError> {
        info!(
            "Renaming token: {} to: {} for user: {}",
            token_id, new_name, user_id
        );

        // 验证令牌属于指定用户
        match self.repository.find_by_id(token_id).await {
            Ok(Some(token)) => {
                if token.user_id != user_id {
                    error!(
                        "Permission denied: token {} does not belong to user {}",
                        token_id, user_id
                    );
                    return Err(CocoError::not_found("Token not found or permission denied"));
                }

                // 重命名令牌
                self.repository
                    .rename_token(token_id, &new_name)
                    .await
                    .map_err(|e| CocoError::server(&format!("Failed to rename token: {}", e)))?;

                info!("Token renamed successfully: {}", token_id);
                Ok(())
            }
            Ok(None) => {
                error!("Token not found: {}", token_id);
                Err(CocoError::not_found("Token not found"))
            }
            Err(e) => {
                error!("Database error during token rename: {}", e);
                Err(CocoError::server("Token rename failed"))
            }
        }
    }

    /// 更新令牌最后使用时间
    pub async fn update_last_used(&self, token: &str) -> Result<(), CocoError> {
        self.repository.update_last_used(token).await.map_err(|e| {
            debug!("Failed to update last used time for token: {}", e);
            CocoError::server("Failed to update token usage")
        })?;

        debug!("Updated last used time for token");
        Ok(())
    }

    /// 获取令牌统计信息
    pub async fn get_token_stats(&self) -> Result<HashMap<String, usize>, CocoError> {
        debug!("Getting token statistics");

        // 这里需要实现数据库查询来获取统计信息
        // 由于SurrealDB的复杂查询，我们暂时返回基本统计
        let mut stats = HashMap::new();

        // 注意：这里需要根据实际需求实现更复杂的统计查询
        // 目前返回基本信息，可以后续扩展
        stats.insert("message".to_string(), 0); // 占位符，表示需要实现具体的统计逻辑

        debug!("Token statistics retrieved");
        Ok(stats)
    }

    /// 清理过期令牌
    pub async fn cleanup_expired_tokens(&self) -> Result<usize, CocoError> {
        info!("Starting cleanup of expired tokens");

        match self.repository.delete_expired_tokens().await {
            Ok(count) => {
                info!("Cleaned up {} expired tokens", count);
                Ok(count)
            }
            Err(e) => {
                error!("Failed to cleanup expired tokens: {}", e);
                Err(CocoError::server("Failed to cleanup expired tokens"))
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::database::{DatabaseConfig, SurrealDBClient};

    async fn create_test_service() -> TokenService {
        // 注意：这些测试需要运行中的SurrealDB实例
        let config = DatabaseConfig::default();
        let client = Arc::new(SurrealDBClient::new(config).await.unwrap());
        let repository = Arc::new(TokenRepository::new_with_global_db());
        TokenService::new(repository)
    }

    #[tokio::test]
    #[ignore] // 需要运行中的SurrealDB实例
    async fn test_generate_and_validate_token() {
        let service = create_test_service().await;
        let user_id = "test-user";
        let token_name = Some("test-token".to_string());

        // 生成令牌
        let token = service
            .generate_api_token(user_id, token_name)
            .await
            .unwrap();
        assert_eq!(token.user_id, user_id);
        assert_eq!(token.name, "test-token");

        // 验证令牌
        let validated = service
            .validate_api_token(&token.access_token)
            .await
            .unwrap();
        assert_eq!(validated.id, token.id);
    }

    #[tokio::test]
    #[ignore] // 需要运行中的SurrealDB实例
    async fn test_revoke_token() {
        let service = create_test_service().await;
        let user_id = "test-user";

        // 生成令牌
        let token = service.generate_api_token(user_id, None).await.unwrap();

        // 撤销令牌
        service.revoke_token(&token.id, user_id).await.unwrap();

        // 验证令牌应该失败（因为已被撤销）
        let result = service.validate_api_token(&token.access_token).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    #[ignore] // 需要运行中的SurrealDB实例
    async fn test_get_user_tokens() {
        let service = create_test_service().await;
        let user_id = "test-user";

        // 生成多个令牌
        let _token1 = service
            .generate_api_token(user_id, Some("token1".to_string()))
            .await
            .unwrap();
        let _token2 = service
            .generate_api_token(user_id, Some("token2".to_string()))
            .await
            .unwrap();

        // 获取用户令牌
        let user_tokens = service.get_user_tokens(user_id).await.unwrap();
        assert_eq!(user_tokens.len(), 2);
    }

    #[tokio::test]
    #[ignore] // 需要运行中的SurrealDB实例
    async fn test_rename_token() {
        let service = create_test_service().await;
        let user_id = "test-user";

        // 生成令牌
        let token = service
            .generate_api_token(user_id, Some("old-name".to_string()))
            .await
            .unwrap();

        // 重命名令牌
        service
            .rename_token(&token.id, user_id, "new-name".to_string())
            .await
            .unwrap();

        // 验证名称已更改
        let user_tokens = service.get_user_tokens(user_id).await.unwrap();
        assert_eq!(user_tokens[0].name, "new-name");
    }

    #[test]
    fn test_token_manager_alias() {
        // 测试TokenManager是TokenService的别名
        // 验证TokenManager和TokenService是相同的类型
        let _type_check: fn(TokenManager) -> TokenService = |tm| tm;
        let _type_check: fn(TokenService) -> TokenManager = |ts| ts;

        // 这个测试验证类型别名定义正确
        assert!(true);
    }

    #[test]
    fn test_token_service_clone() {
        // 测试TokenService的Clone实现
        // 这个测试主要验证结构体定义正确，实际的Clone功能在集成测试中验证
        // 由于我们使用了Arc，Clone应该是浅拷贝
        assert!(true);
    }

    #[test]
    fn test_error_handling_types() {
        use crate::error::error::CocoError;

        // 测试错误类型的创建
        let _unauthorized = CocoError::unauthorized("test");
        let _server_error = CocoError::server("test");
        let _not_found = CocoError::not_found("test");

        // 验证错误类型可以正确创建
        assert!(true);
    }
}
