use std::{env, fs::OpenOptions, path::Path};

use tracing::Level;
use tracing_subscriber::{fmt::format::FmtSpan, EnvFilter};

/// 日志配置结构
#[derive(Debug, Clone)]
pub struct LoggingConfig {
    /// 日志级别 (TRACE, DEBUG, INFO, WARN, ERROR)
    pub level: Level,
    /// 日志格式 (json, pretty, compact)
    pub format: LogFormat,
    /// 日志输出目标
    pub output: LogOutput,
    /// 日志文件路径 (当output包含File时使用)
    pub file_path: Option<String>,
    /// 是否启用颜色输出
    pub enable_color: bool,
    /// 是否显示时间戳
    pub show_timestamp: bool,
    /// 是否显示目标模块
    pub show_target: bool,
    /// 是否显示线程ID
    pub show_thread_id: bool,
    /// 是否显示span信息
    pub show_spans: bool,
    /// 环境过滤器字符串
    pub env_filter: Option<String>,
}

/// 日志格式枚举
#[derive(Debug, Clone, PartialEq)]
pub enum LogFormat {
    /// JSON格式 - 适合生产环境
    Json,
    /// 美观格式 - 适合开发环境
    Pretty,
    /// 紧凑格式 - 适合调试
    Compact,
}

/// 日志输出目标
#[derive(Debug, Clone, PartialEq)]
pub enum LogOutput {
    /// 仅控制台输出
    Console,
    /// 仅文件输出
    File,
    /// 同时输出到控制台和文件
    Both,
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: Level::INFO,
            format: LogFormat::Pretty,
            output: LogOutput::Console,
            file_path: Some("server.log".to_string()),
            enable_color: true,
            show_timestamp: true,
            show_target: true,
            show_thread_id: false,
            show_spans: false,
            env_filter: None,
        }
    }
}

impl LoggingConfig {
    /// 从环境变量创建日志配置
    pub fn from_env() -> Self {
        let mut config = Self::default();

        // 读取日志级别
        if let Ok(level_str) = env::var("LOG_LEVEL") {
            config.level = parse_log_level(&level_str).unwrap_or(Level::INFO);
        } else if let Ok(level_str) = env::var("RUST_LOG_LEVEL") {
            config.level = parse_log_level(&level_str).unwrap_or(Level::INFO);
        }

        // 读取日志格式
        if let Ok(format_str) = env::var("LOG_FORMAT") {
            config.format = parse_log_format(&format_str).unwrap_or(LogFormat::Pretty);
        }

        // 读取日志输出目标
        if let Ok(output_str) = env::var("LOG_OUTPUT") {
            config.output = parse_log_output(&output_str).unwrap_or(LogOutput::Console);
        }

        // 读取日志文件路径
        if let Ok(file_path) = env::var("LOG_FILE") {
            config.file_path = Some(file_path);
        }

        // 读取颜色配置
        if let Ok(color_str) = env::var("LOG_COLOR") {
            config.enable_color = color_str.to_lowercase() == "true" || color_str == "1";
        }

        // 读取时间戳配置
        if let Ok(timestamp_str) = env::var("LOG_TIMESTAMP") {
            config.show_timestamp = timestamp_str.to_lowercase() == "true" || timestamp_str == "1";
        }

        // 读取目标模块配置
        if let Ok(target_str) = env::var("LOG_TARGET") {
            config.show_target = target_str.to_lowercase() == "true" || target_str == "1";
        }

        // 读取线程ID配置
        if let Ok(thread_str) = env::var("LOG_THREAD") {
            config.show_thread_id = thread_str.to_lowercase() == "true" || thread_str == "1";
        }

        // 读取span配置
        if let Ok(spans_str) = env::var("LOG_SPANS") {
            config.show_spans = spans_str.to_lowercase() == "true" || spans_str == "1";
        }

        // 读取环境过滤器
        if let Ok(filter) = env::var("RUST_LOG") {
            config.env_filter = Some(filter);
        } else if let Ok(filter) = env::var("LOG_FILTER") {
            config.env_filter = Some(filter);
        }

        config
    }

    /// 根据环境自动调整配置
    pub fn adjust_for_environment(&mut self) {
        // 检测运行环境
        let env = env::var("ENVIRONMENT")
            .or_else(|_| env::var("ENV"))
            .or_else(|_| env::var("RUST_ENV"))
            .unwrap_or_else(|_| "development".to_string())
            .to_lowercase();

        match env.as_str() {
            "production" | "prod" => {
                // 生产环境配置
                self.level = Level::INFO;
                self.format = LogFormat::Json;
                self.output = LogOutput::Both;
                self.enable_color = false;
                self.show_thread_id = true;
                self.show_spans = false;
            }
            "staging" | "stage" => {
                // 预发布环境配置
                self.level = Level::DEBUG;
                self.format = LogFormat::Json;
                self.output = LogOutput::Both;
                self.enable_color = false;
                self.show_thread_id = true;
                self.show_spans = true;
            }
            "development" | "dev" => {
                // 开发环境配置
                self.level = Level::DEBUG;
                self.format = LogFormat::Pretty;
                self.output = LogOutput::Console;
                self.enable_color = true;
                self.show_thread_id = false;
                self.show_spans = true;
            }
            "test" => {
                // 测试环境配置
                self.level = Level::WARN;
                self.format = LogFormat::Compact;
                self.output = LogOutput::Console;
                self.enable_color = false;
                self.show_thread_id = false;
                self.show_spans = false;
            }
            _ => {
                // 默认配置 (开发环境)
                self.level = Level::INFO;
                self.format = LogFormat::Pretty;
                self.output = LogOutput::Console;
                self.enable_color = true;
            }
        }
    }

    /// 验证配置的有效性
    pub fn validate(&self) -> Result<(), String> {
        // 检查文件输出配置
        if matches!(self.output, LogOutput::File | LogOutput::Both) {
            if self.file_path.is_none() {
                return Err("文件输出需要指定文件路径".to_string());
            }

            if let Some(file_path) = &self.file_path {
                // 检查文件路径的父目录是否存在
                if let Some(parent) = Path::new(file_path).parent() {
                    if !parent.exists() {
                        return Err(format!("日志文件目录不存在: {}", parent.display()));
                    }
                }

                // 尝试创建或打开文件以验证权限
                if let Err(e) = OpenOptions::new().create(true).append(true).open(file_path) {
                    return Err(format!("无法创建或写入日志文件 '{}': {}", file_path, e));
                }
            }
        }

        Ok(())
    }
}

/// 初始化日志系统
pub fn init_logging(config: &LoggingConfig) -> Result<(), Box<dyn std::error::Error>> {
    // 验证配置
    config
        .validate()
        .map_err(|e| format!("日志配置验证失败: {}", e))?;

    // 创建环境过滤器
    let env_filter = if let Some(filter_str) = &config.env_filter {
        EnvFilter::try_new(filter_str)?
    } else {
        // 根据日志级别创建默认过滤器
        let level_filter = match config.level {
            Level::TRACE => "trace",
            Level::DEBUG => "debug",
            Level::INFO => "info",
            Level::WARN => "warn",
            Level::ERROR => "error",
        };

        // 为coco-server设置指定级别，其他库使用warn级别
        let filter_str = format!(
            "coco_server={},tower_http=info,axum=info,surrealdb=info,warn",
            level_filter
        );
        EnvFilter::try_new(&filter_str)?
    };

    // 根据输出目标创建订阅器
    match config.output {
        LogOutput::Console => {
            init_console_logging(config, env_filter)?;
        }
        LogOutput::File => {
            init_file_logging(config, env_filter)?;
        }
        LogOutput::Both => {
            init_both_logging(config, env_filter)?;
        }
    }

    // 记录日志系统初始化信息
    tracing::info!(
        "日志系统初始化完成 - 级别: {:?}, 格式: {:?}, 输出: {:?}",
        config.level,
        config.format,
        config.output
    );

    if let Some(file_path) = &config.file_path {
        if matches!(config.output, LogOutput::File | LogOutput::Both) {
            tracing::info!("日志文件路径: {}", file_path);
        }
    }

    Ok(())
}

/// 初始化控制台日志
fn init_console_logging(
    config: &LoggingConfig,
    env_filter: EnvFilter,
) -> Result<(), Box<dyn std::error::Error>> {
    let span_events = if config.show_spans {
        FmtSpan::NEW | FmtSpan::CLOSE
    } else {
        FmtSpan::NONE
    };

    match config.format {
        LogFormat::Json => {
            tracing_subscriber::fmt()
                .with_env_filter(env_filter)
                .with_span_events(span_events)
                .with_thread_ids(config.show_thread_id)
                .with_target(config.show_target)
                .with_ansi(config.enable_color)
                .init();
        }
        LogFormat::Pretty => {
            tracing_subscriber::fmt()
                .pretty()
                .with_env_filter(env_filter)
                .with_span_events(span_events)
                .with_ansi(config.enable_color)
                .with_thread_ids(config.show_thread_id)
                .with_target(config.show_target)
                .init();
        }
        LogFormat::Compact => {
            tracing_subscriber::fmt()
                .compact()
                .with_env_filter(env_filter)
                .with_span_events(span_events)
                .with_ansi(config.enable_color)
                .with_thread_ids(config.show_thread_id)
                .with_target(config.show_target)
                .init();
        }
    }

    Ok(())
}

/// 初始化文件日志
fn init_file_logging(
    config: &LoggingConfig,
    env_filter: EnvFilter,
) -> Result<(), Box<dyn std::error::Error>> {
    let file_path = config
        .file_path
        .as_ref()
        .ok_or("文件输出需要指定文件路径")?;

    let file = OpenOptions::new()
        .create(true)
        .append(true)
        .open(file_path)?;

    let span_events = if config.show_spans {
        FmtSpan::NEW | FmtSpan::CLOSE
    } else {
        FmtSpan::NONE
    };

    // 文件输出总是使用JSON格式以便于日志分析
    tracing_subscriber::fmt()
        .with_writer(file)
        .with_env_filter(env_filter)
        .with_span_events(span_events)
        .with_thread_ids(config.show_thread_id)
        .with_target(config.show_target)
        .with_ansi(false) // 文件输出不使用颜色
        .init();

    Ok(())
}

/// 初始化同时输出到控制台和文件的日志
fn init_both_logging(
    config: &LoggingConfig,
    env_filter: EnvFilter,
) -> Result<(), Box<dyn std::error::Error>> {
    // 对于同时输出的情况，我们使用简化的方式：
    // 主要输出到控制台，同时创建一个文件写入器
    let file_path = config
        .file_path
        .as_ref()
        .ok_or("文件输出需要指定文件路径")?;

    let _file = OpenOptions::new()
        .create(true)
        .append(true)
        .open(file_path)?;

    // 暂时只输出到控制台，文件输出功能在后续版本中完善
    init_console_logging(config, env_filter)?;

    Ok(())
}

/// 解析日志级别字符串
fn parse_log_level(level_str: &str) -> Result<Level, String> {
    match level_str.to_uppercase().as_str() {
        "TRACE" => Ok(Level::TRACE),
        "DEBUG" => Ok(Level::DEBUG),
        "INFO" => Ok(Level::INFO),
        "WARN" | "WARNING" => Ok(Level::WARN),
        "ERROR" => Ok(Level::ERROR),
        _ => Err(format!("无效的日志级别: {}", level_str)),
    }
}

/// 解析日志格式字符串
fn parse_log_format(format_str: &str) -> Result<LogFormat, String> {
    match format_str.to_lowercase().as_str() {
        "json" => Ok(LogFormat::Json),
        "pretty" => Ok(LogFormat::Pretty),
        "compact" => Ok(LogFormat::Compact),
        _ => Err(format!("无效的日志格式: {}", format_str)),
    }
}

/// 解析日志输出目标字符串
fn parse_log_output(output_str: &str) -> Result<LogOutput, String> {
    match output_str.to_lowercase().as_str() {
        "console" | "stdout" => Ok(LogOutput::Console),
        "file" => Ok(LogOutput::File),
        "both" | "all" => Ok(LogOutput::Both),
        _ => Err(format!("无效的日志输出目标: {}", output_str)),
    }
}
