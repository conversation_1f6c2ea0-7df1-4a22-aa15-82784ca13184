use serde::{Deserialize, Serialize};
use serde_json::Value;

use crate::models::model_provider::ModelProvider;

/// 模型提供商API响应格式化器
///
/// 负责将业务数据转换为符合API规格的响应格式
pub struct ResponseFormatter;

/// 创建模型提供商响应
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateModelProviderResponse {
    #[serde(rename = "_id")]
    pub id: String,
    pub result: String,
}

/// 获取模型提供商响应
#[derive(Debug, Serialize, Deserialize)]
pub struct GetModelProviderResponse {
    #[serde(rename = "_id")]
    pub id: String,
    pub found: bool,
    #[serde(rename = "_source", skip_serializing_if = "Option::is_none")]
    pub source: Option<ModelProviderSource>,
}

/// 模型提供商源数据（不包含敏感信息）
#[derive(Debug, Serialize, Deserialize)]
pub struct ModelProviderSource {
    pub id: String,
    pub created: chrono::DateTime<chrono::Utc>,
    pub updated: chrono::DateTime<chrono::Utc>,
    pub name: String,
    pub api_type: String,
    pub base_url: String,
    pub icon: String,
    pub models: Vec<Value>,
    pub enabled: bool,
    pub builtin: bool,
    pub description: String,
    // 注意：不包含api_key等敏感字段
}

/// 更新模型提供商响应
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateModelProviderResponse {
    #[serde(rename = "_id")]
    pub id: String,
    pub result: String,
}

/// 删除模型提供商响应
#[derive(Debug, Serialize, Deserialize)]
pub struct DeleteModelProviderResponse {
    #[serde(rename = "_id")]
    pub id: String,
    pub result: String,
}

/// 错误响应
#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error: String,
    pub message: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub details: Option<Value>,
}

impl ResponseFormatter {
    /// 格式化创建成功响应
    ///
    /// # 参数
    /// * `id` - 创建的模型提供商ID
    pub fn format_create_success(id: String) -> CreateModelProviderResponse {
        CreateModelProviderResponse {
            id,
            result: "created".to_string(),
        }
    }

    /// 格式化获取成功响应
    ///
    /// # 参数
    /// * `provider` - 模型提供商数据
    pub fn format_get_success(provider: ModelProvider) -> GetModelProviderResponse {
        let source = ModelProviderSource {
            id: provider.id.clone(),
            created: provider.created,
            updated: provider.updated,
            name: provider.name,
            api_type: provider.api_type,
            base_url: provider.base_url,
            icon: provider.icon,
            models: provider
                .models
                .into_iter()
                .map(|model| serde_json::to_value(model).unwrap_or_default())
                .collect(),
            enabled: provider.enabled,
            builtin: provider.builtin,
            description: provider.description,
            // 故意不包含api_key
        };

        GetModelProviderResponse {
            id: provider.id,
            found: true,
            source: Some(source),
        }
    }

    /// 格式化获取失败响应（未找到）
    ///
    /// # 参数
    /// * `id` - 请求的模型提供商ID
    pub fn format_get_not_found(id: String) -> GetModelProviderResponse {
        GetModelProviderResponse {
            id,
            found: false,
            source: None,
        }
    }

    /// 格式化更新成功响应
    ///
    /// # 参数
    /// * `id` - 更新的模型提供商ID
    pub fn format_update_success(id: String) -> UpdateModelProviderResponse {
        UpdateModelProviderResponse {
            id,
            result: "updated".to_string(),
        }
    }

    /// 格式化删除成功响应
    ///
    /// # 参数
    /// * `id` - 删除的模型提供商ID
    pub fn format_delete_success(id: String) -> DeleteModelProviderResponse {
        DeleteModelProviderResponse {
            id,
            result: "deleted".to_string(),
        }
    }

    /// 格式化错误响应
    ///
    /// # 参数
    /// * `error` - 错误类型
    /// * `message` - 错误消息
    /// * `details` - 可选的错误详情
    pub fn format_error(error: &str, message: &str, details: Option<Value>) -> ErrorResponse {
        ErrorResponse {
            error: error.to_string(),
            message: message.to_string(),
            details,
        }
    }

    /// 格式化验证错误响应
    ///
    /// # 参数
    /// * `message` - 验证错误消息
    pub fn format_validation_error(message: &str) -> ErrorResponse {
        Self::format_error("validation_error", message, None)
    }

    /// 格式化冲突错误响应
    ///
    /// # 参数
    /// * `message` - 冲突错误消息
    pub fn format_conflict_error(message: &str) -> ErrorResponse {
        Self::format_error("conflict", message, None)
    }

    /// 格式化未找到错误响应
    ///
    /// # 参数
    /// * `message` - 未找到错误消息
    pub fn format_not_found_error(message: &str) -> ErrorResponse {
        Self::format_error("not_found", message, None)
    }

    /// 格式化内部服务器错误响应
    ///
    /// # 参数
    /// * `message` - 错误消息
    pub fn format_internal_error(message: &str) -> ErrorResponse {
        Self::format_error("internal_error", message, None)
    }

    /// 格式化权限错误响应
    ///
    /// # 参数
    /// * `message` - 权限错误消息
    pub fn format_permission_error(message: &str) -> ErrorResponse {
        Self::format_error("permission_denied", message, None)
    }
}

#[cfg(test)]
mod tests {
    use chrono::Utc;

    use super::*;

    fn create_test_provider() -> ModelProvider {
        ModelProvider {
            id: "test-id".to_string(),
            created: Utc::now(),
            updated: Utc::now(),
            name: "Test Provider".to_string(),
            api_key: "secret-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.test.com".to_string(),
            icon: "test-icon".to_string(),
            models: vec![],
            enabled: true,
            builtin: false,
            description: "Test description".to_string(),
        }
    }

    #[test]
    fn test_format_create_success() {
        let response = ResponseFormatter::format_create_success("test-id".to_string());
        assert_eq!(response.id, "test-id");
        assert_eq!(response.result, "created");
    }

    #[test]
    fn test_format_get_success() {
        let provider = create_test_provider();
        let response = ResponseFormatter::format_get_success(provider);

        assert_eq!(response.id, "test-id");
        assert!(response.found);
        assert!(response.source.is_some());

        let source = response.source.unwrap();
        assert_eq!(source.name, "Test Provider");
        assert_eq!(source.api_type, "openai");
        // 确保不包含敏感信息
        // api_key字段不应该在source中
    }

    #[test]
    fn test_format_get_not_found() {
        let response = ResponseFormatter::format_get_not_found("test-id".to_string());
        assert_eq!(response.id, "test-id");
        assert!(!response.found);
        assert!(response.source.is_none());
    }

    #[test]
    fn test_format_error() {
        let response = ResponseFormatter::format_validation_error("Invalid input");
        assert_eq!(response.error, "validation_error");
        assert_eq!(response.message, "Invalid input");
        assert!(response.details.is_none());
    }
}
