use std::sync::Arc;

use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::J<PERSON>,
};
use tracing::{error, info, warn};

use crate::{
    config::config_manager::ConfigManager,
    error::error::CocoError,
    handlers::response_formatter::{
        CreateModelProviderResponse, GetModelProviderResponse, ResponseFormatter,
    },
    models::model_provider::CreateModelProviderRequest,
    repositories::{
        cache_manager::CacheManager, model_provider_repo::SurrealModelProviderRepository,
    },
    services::{
        cache_service::CacheService,
        model_provider_service::{ModelProviderService, ModelProviderServiceTrait},
        validation_service::ValidationService,
    },
};

/// 模型提供商API处理器
///
/// 处理模型提供商相关的HTTP请求
pub struct ModelProviderHandler {
    service: Arc<ModelProviderService>,
}

impl ModelProviderHandler {
    /// 创建新的模型提供商处理器
    ///
    /// # 参数
    /// * `service` - 模型提供商服务
    pub fn new(service: Arc<ModelProviderService>) -> Self {
        Self { service }
    }
}

/// 创建模型提供商处理器
///
/// 处理 POST /model_provider/ 请求
pub async fn create_model_provider_handler(
    State(config_manager): State<Arc<ConfigManager>>,
    Json(request): Json<CreateModelProviderRequest>,
) -> Result<(StatusCode, Json<CreateModelProviderResponse>), (StatusCode, Json<serde_json::Value>)>
{
    info!("处理创建模型提供商请求: name={}", request.name);

    // 获取或创建模型提供商服务
    let service = get_or_create_service().await;

    // 调用业务服务创建模型提供商
    match service.create(request).await {
        Ok(id) => {
            info!("模型提供商创建成功: id={}", id);
            let response = ResponseFormatter::format_create_success(id);
            Ok((StatusCode::CREATED, Json(response)))
        }
        Err(e) => {
            error!("创建模型提供商失败: {}", e);
            let (status_code, error_response) = map_error_to_response(&e);
            Err((status_code, Json(error_response)))
        }
    }
}

/// 获取模型提供商处理器
///
/// 处理 GET /model_provider/:id 请求
pub async fn get_model_provider_handler(
    State(config_manager): State<Arc<ConfigManager>>,
    Path(id): Path<String>,
) -> Result<Json<GetModelProviderResponse>, (StatusCode, Json<serde_json::Value>)> {
    info!("处理获取模型提供商请求: id={}", id);

    // 获取或创建模型提供商服务
    let service = get_or_create_service().await;

    // 调用业务服务获取模型提供商
    match service.get_by_id(&id).await {
        Ok(Some(provider)) => {
            info!("模型提供商获取成功: id={}", id);
            let response = ResponseFormatter::format_get_success(provider);
            Ok(Json(response))
        }
        Ok(None) => {
            warn!("模型提供商未找到: id={}", id);
            let response = ResponseFormatter::format_get_not_found(id);
            Ok(Json(response))
        }
        Err(e) => {
            error!("获取模型提供商失败: id={}, error={}", id, e);
            let (status_code, error_response) = map_error_to_response(&e);
            Err((status_code, Json(error_response)))
        }
    }
}

/// 获取或创建模型提供商服务
///
/// # 参数
/// * `app_state` - 应用状态
async fn get_or_create_service() -> Arc<ModelProviderService> {
    // 创建依赖服务
    let db = Arc::new(crate::database::DB.clone());

    // 创建Repository
    let repository = Arc::new(SurrealModelProviderRepository::new(db));

    // 创建CacheService
    let cache_manager = Arc::new(CacheManager::new(1800)); // 30分钟TTL
    let cache_service = Arc::new(CacheService::new(cache_manager));

    // 创建ValidationService
    let validation_service = Arc::new(ValidationService::new());

    // 创建ModelProviderService
    Arc::new(ModelProviderService::new(
        repository,
        cache_service,
        validation_service,
    ))
}

/// 将业务错误映射为HTTP响应
///
/// # 参数
/// * `error` - 业务错误
fn map_error_to_response(error: &CocoError) -> (StatusCode, serde_json::Value) {
    match error {
        CocoError::ModelProviderValidation(msg) => {
            let response = ResponseFormatter::format_validation_error(msg);
            (
                StatusCode::BAD_REQUEST,
                serde_json::to_value(response).unwrap(),
            )
        }
        CocoError::ModelProviderNotFound(msg) => {
            let response = ResponseFormatter::format_not_found_error(msg);
            (
                StatusCode::NOT_FOUND,
                serde_json::to_value(response).unwrap(),
            )
        }
        CocoError::BuiltinProviderProtection(msg) => {
            let response = ResponseFormatter::format_conflict_error(msg);
            (
                StatusCode::CONFLICT,
                serde_json::to_value(response).unwrap(),
            )
        }
        CocoError::Repository(msg) => {
            let response =
                ResponseFormatter::format_internal_error(&format!("数据库错误: {}", msg));
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                serde_json::to_value(response).unwrap(),
            )
        }
        CocoError::Database(msg) => {
            let response =
                ResponseFormatter::format_internal_error(&format!("数据库连接错误: {}", msg));
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                serde_json::to_value(response).unwrap(),
            )
        }
        CocoError::JsonError(e) => {
            let response = ResponseFormatter::format_internal_error(&format!("序列化错误: {}", e));
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                serde_json::to_value(response).unwrap(),
            )
        }
        _ => {
            let response = ResponseFormatter::format_internal_error("内部服务器错误");
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                serde_json::to_value(response).unwrap(),
            )
        }
    }
}

#[cfg(test)]
mod tests {
    use axum::http::StatusCode;

    use super::*;
    use crate::models::model_provider::CreateModelProviderRequest;

    fn create_test_request() -> CreateModelProviderRequest {
        CreateModelProviderRequest {
            name: "Test Provider".to_string(),
            api_key: "test-api-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.test.com".to_string(),
            icon: "test-icon".to_string(),
            models: vec![],
            enabled: true,
            description: "Test description".to_string(),
        }
    }

    #[test]
    fn test_map_error_to_response() {
        let error = CocoError::model_provider_validation("Invalid input");
        let (status, response) = map_error_to_response(&error);

        assert_eq!(status, StatusCode::BAD_REQUEST);

        let response_value: serde_json::Value = response;
        assert_eq!(response_value["error"], "validation_error");
        assert_eq!(response_value["message"], "Invalid input");
    }

    #[test]
    fn test_map_not_found_error() {
        let error = CocoError::model_provider_not_found("test-id");
        let (status, response) = map_error_to_response(&error);

        assert_eq!(status, StatusCode::NOT_FOUND);

        let response_value: serde_json::Value = response;
        assert_eq!(response_value["error"], "not_found");
    }

    #[test]
    fn test_map_builtin_protection_error() {
        let error = CocoError::builtin_provider_protection("Cannot delete builtin provider");
        let (status, response) = map_error_to_response(&error);

        assert_eq!(status, StatusCode::CONFLICT);

        let response_value: serde_json::Value = response;
        assert_eq!(response_value["error"], "conflict");
    }
}
