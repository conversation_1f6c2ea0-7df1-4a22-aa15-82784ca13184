use serde::{Deserialize, Serialize};

use crate::database::DatabaseConfig;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct Config {
    pub env: Option<EnvConfig>,
    pub coco: Option<CocoConfig>,
    pub database: Option<DatabaseConfig>,
    pub logging: Option<LoggingConfigModel>,
    // 其他配置项可以根据需要添加
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct EnvConfig {
    // SurrealDB配置
    #[serde(rename = "SURREALDB_URL")]
    pub surrealdb_url: Option<String>,
    #[serde(rename = "SURREALDB_NAMESPACE")]
    pub surrealdb_namespace: Option<String>,
    #[serde(rename = "SURREALDB_DATABASE")]
    pub surrealdb_database: Option<String>,
    #[serde(rename = "SURREALDB_USERNAME")]
    pub surrealdb_username: Option<String>,
    #[serde(rename = "SURREALDB_PASSWORD")]
    pub surrealdb_password: Option<String>,

    // 服务器配置
    #[serde(rename = "WEB_BINDING")]
    pub web_binding: Option<String>,
    #[serde(rename = "API_BINDING")]
    pub api_binding: Option<String>,

    // 保留ES配置以便迁移期间使用
    #[serde(rename = "ES_ENDPOINT")]
    pub es_endpoint: Option<String>,
    #[serde(rename = "ES_USERNAME")]
    pub es_username: Option<String>,
    #[serde(rename = "ES_PASSWORD")]
    pub es_password: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct CocoConfig {
    pub server: Option<CocoServerConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct CocoServerConfig {
    pub public: Option<bool>,
    pub name: Option<String>,
    #[serde(rename = "encode_icon_to_base64")]
    pub encode_icon_to_base64: Option<bool>,
    #[serde(rename = "minimal_client_version")]
    pub minimal_client_version: Option<VersionConfig>,
    pub provider: Option<ProviderConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct VersionConfig {
    pub number: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ProviderConfig {
    pub name: Option<String>,
    pub description: Option<String>,
    pub icon: Option<String>,
    pub website: Option<String>,
    #[serde(rename = "eula")]
    pub eula: Option<String>,
    #[serde(rename = "privacy_policy")]
    pub privacy_policy: Option<String>,
    pub banner: Option<String>,
    #[serde(rename = "auth_provider")]
    pub auth_provider: Option<AuthProviderConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct AuthProviderConfig {
    pub sso: Option<SsoConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct SsoConfig {
    pub url: Option<String>,
}

/// 日志配置模型 - 用于配置文件序列化
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct LoggingConfigModel {
    /// 日志级别 (TRACE, DEBUG, INFO, WARN, ERROR)
    pub level: Option<String>,
    /// 日志格式 (json, pretty, compact)
    pub format: Option<String>,
    /// 日志输出目标 (console, file, both)
    pub output: Option<String>,
    /// 日志文件路径
    pub file_path: Option<String>,
    /// 是否启用颜色输出
    pub enable_color: Option<bool>,
    /// 是否显示时间戳
    pub show_timestamp: Option<bool>,
    /// 是否显示目标模块
    pub show_target: Option<bool>,
    /// 是否显示线程ID
    pub show_thread_id: Option<bool>,
    /// 是否显示span信息
    pub show_spans: Option<bool>,
    /// 环境过滤器字符串
    pub env_filter: Option<String>,
}
