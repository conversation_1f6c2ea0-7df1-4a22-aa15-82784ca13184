use std::env;

use tracing::Level;

use crate::{
    config::models::Config,
    database::DatabaseConfig,
    error::{error::CocoError, result::Result},
    logging::{LogFormat, LogOutput, LoggingConfig},
};

/// 应用配置结构
/// 整合了所有应用级别的配置，包括日志、数据库、服务器等
#[derive(Debug, <PERSON>lone)]
pub struct AppConfig {
    /// 日志配置
    pub logging: LoggingConfig,
    /// 数据库配置
    pub database: DatabaseConfig,
    /// 服务器配置
    pub server: ServerConfig,
    /// 应用元信息
    pub app_info: AppInfo,
}

/// 服务器配置
#[derive(Debug, Clone)]
pub struct ServerConfig {
    /// Web服务绑定地址
    pub web_binding: String,
    /// API服务绑定地址
    pub api_binding: String,
    /// 是否启用HTTPS
    pub enable_https: bool,
    /// 是否为公开服务器
    pub public: bool,
    /// 服务器名称
    pub name: String,
    /// 请求超时时间（秒）
    pub request_timeout: u64,
    /// 最大并发连接数
    pub max_connections: usize,
}

/// 应用信息
#[derive(Debug, Clone)]
pub struct AppInfo {
    /// 应用名称
    pub name: String,
    /// 应用版本
    pub version: String,
    /// 运行环境
    pub environment: String,
    /// 启动时间
    pub start_time: chrono::DateTime<chrono::Utc>,
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            web_binding: "0.0.0.0:9000".to_string(),
            api_binding: "0.0.0.0:2900".to_string(),
            enable_https: false,
            public: false,
            name: "Coco Server".to_string(),
            request_timeout: 30,
            max_connections: 1000,
        }
    }
}

impl Default for AppInfo {
    fn default() -> Self {
        Self {
            name: "coco-server".to_string(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            environment: "development".to_string(),
            start_time: chrono::Utc::now(),
        }
    }
}

impl AppConfig {
    /// 从配置文件和环境变量创建应用配置
    pub fn from_config(config: &Config) -> Result<Self> {
        // 创建日志配置
        let logging = Self::create_logging_config(config)?;

        // 创建数据库配置
        let database = Self::create_database_config(config)?;

        // 创建服务器配置
        let server = Self::create_server_config(config)?;

        // 创建应用信息
        let app_info = Self::create_app_info(config)?;

        Ok(AppConfig {
            logging,
            database,
            server,
            app_info,
        })
    }

    /// 创建日志配置
    fn create_logging_config(config: &Config) -> Result<LoggingConfig> {
        let mut logging_config = LoggingConfig::from_env();

        // 从配置文件读取日志配置
        if let Some(logging_model) = config.logging.as_ref() {
            // 应用配置文件中的设置
            if let Some(level_str) = &logging_model.level {
                if let Ok(level) = Self::parse_log_level(level_str) {
                    logging_config.level = level;
                }
            }

            if let Some(format_str) = &logging_model.format {
                if let Ok(format) = Self::parse_log_format(format_str) {
                    logging_config.format = format;
                }
            }

            if let Some(output_str) = &logging_model.output {
                if let Ok(output) = Self::parse_log_output(output_str) {
                    logging_config.output = output;
                }
            }

            if let Some(file_path) = &logging_model.file_path {
                logging_config.file_path = Some(file_path.clone());
            }

            if let Some(enable_color) = logging_model.enable_color {
                logging_config.enable_color = enable_color;
            }

            if let Some(show_timestamp) = logging_model.show_timestamp {
                logging_config.show_timestamp = show_timestamp;
            }

            if let Some(show_target) = logging_model.show_target {
                logging_config.show_target = show_target;
            }

            if let Some(show_thread_id) = logging_model.show_thread_id {
                logging_config.show_thread_id = show_thread_id;
            }

            if let Some(show_spans) = logging_model.show_spans {
                logging_config.show_spans = show_spans;
            }

            if let Some(env_filter) = &logging_model.env_filter {
                logging_config.env_filter = Some(env_filter.clone());
            }
        }

        // 根据环境自动调整配置
        logging_config.adjust_for_environment();

        // 验证配置
        logging_config
            .validate()
            .map_err(|e| CocoError::ConfigError(format!("日志配置验证失败: {}", e)))?;

        Ok(logging_config)
    }

    /// 创建数据库配置
    fn create_database_config(config: &Config) -> Result<DatabaseConfig> {
        let mut db_config = config.database.clone().unwrap_or_default();

        // 从环境变量覆盖配置
        if let Some(env_config) = &config.env {
            if let Some(url) = &env_config.surrealdb_url {
                db_config.url = url.clone();
            }
            if let Some(namespace) = &env_config.surrealdb_namespace {
                db_config.namespace = namespace.clone();
            }
            if let Some(database) = &env_config.surrealdb_database {
                db_config.database = database.clone();
            }
            if let Some(username) = &env_config.surrealdb_username {
                db_config.username = username.clone();
            }
            if let Some(password) = &env_config.surrealdb_password {
                db_config.password = password.clone();
            }
        }

        // 直接从环境变量获取（最高优先级）
        if let Ok(url) = env::var("SURREALDB_URL") {
            db_config.url = url;
        }
        if let Ok(namespace) = env::var("SURREALDB_NAMESPACE") {
            db_config.namespace = namespace;
        }
        if let Ok(database) = env::var("SURREALDB_DATABASE") {
            db_config.database = database;
        }
        if let Ok(username) = env::var("SURREALDB_USERNAME") {
            db_config.username = username;
        }
        if let Ok(password) = env::var("SURREALDB_PASSWORD") {
            db_config.password = password;
        }

        // 验证数据库配置
        db_config.validate()?;

        Ok(db_config)
    }

    /// 创建服务器配置
    fn create_server_config(config: &Config) -> Result<ServerConfig> {
        let mut server_config = ServerConfig::default();

        // 从配置文件读取
        if let Some(env_config) = &config.env {
            if let Some(web_binding) = &env_config.web_binding {
                server_config.web_binding = web_binding.clone();
            }
            if let Some(api_binding) = &env_config.api_binding {
                server_config.api_binding = api_binding.clone();
            }
        }

        if let Some(coco_config) = &config.coco {
            if let Some(server_cfg) = &coco_config.server {
                if let Some(public) = server_cfg.public {
                    server_config.public = public;
                }
                if let Some(name) = &server_cfg.name {
                    server_config.name = name.clone();
                }
            }
        }

        // 从环境变量覆盖
        if let Ok(web_binding) = env::var("WEB_BINDING") {
            server_config.web_binding = web_binding;
        }
        if let Ok(api_binding) = env::var("API_BINDING") {
            server_config.api_binding = api_binding;
        }
        if let Ok(enable_https) = env::var("USE_HTTPS") {
            server_config.enable_https =
                enable_https.to_lowercase() == "true" || enable_https == "1";
        }
        if let Ok(timeout_str) = env::var("REQUEST_TIMEOUT") {
            if let Ok(timeout) = timeout_str.parse::<u64>() {
                server_config.request_timeout = timeout;
            }
        }
        if let Ok(max_conn_str) = env::var("MAX_CONNECTIONS") {
            if let Ok(max_conn) = max_conn_str.parse::<usize>() {
                server_config.max_connections = max_conn;
            }
        }

        Ok(server_config)
    }

    /// 创建应用信息
    fn create_app_info(_config: &Config) -> Result<AppInfo> {
        let mut app_info = AppInfo::default();

        // 从环境变量读取环境信息
        app_info.environment = env::var("ENVIRONMENT")
            .or_else(|_| env::var("ENV"))
            .or_else(|_| env::var("RUST_ENV"))
            .unwrap_or_else(|_| "development".to_string())
            .to_lowercase();

        Ok(app_info)
    }

    /// 验证整个应用配置
    pub fn validate(&self) -> Result<()> {
        // 验证日志配置
        self.logging
            .validate()
            .map_err(|e| CocoError::ConfigError(format!("日志配置验证失败: {}", e)))?;

        // 验证数据库配置
        self.database.validate()?;

        // 验证服务器配置
        self.validate_server_config()?;

        Ok(())
    }

    /// 验证服务器配置
    fn validate_server_config(&self) -> Result<()> {
        // 验证绑定地址格式
        if !self.server.web_binding.contains(':') {
            return Err(CocoError::ConfigError(
                "Web绑定地址格式无效，应为 'host:port' 格式".to_string(),
            ));
        }

        if !self.server.api_binding.contains(':') {
            return Err(CocoError::ConfigError(
                "API绑定地址格式无效，应为 'host:port' 格式".to_string(),
            ));
        }

        // 检查端口冲突
        let web_port = self.extract_port(&self.server.web_binding)?;
        let api_port = self.extract_port(&self.server.api_binding)?;

        if web_port == api_port {
            return Err(CocoError::ConfigError(format!(
                "Web服务端口({})和API服务端口({})冲突，必须使用不同的端口",
                web_port, api_port
            )));
        }

        // 验证超时时间
        if self.server.request_timeout == 0 {
            return Err(CocoError::ConfigError("请求超时时间必须大于0".to_string()));
        }

        // 验证最大连接数
        if self.server.max_connections == 0 {
            return Err(CocoError::ConfigError("最大连接数必须大于0".to_string()));
        }

        Ok(())
    }

    /// 从绑定地址提取端口号
    fn extract_port(&self, binding: &str) -> Result<u16> {
        if let Some(colon_pos) = binding.rfind(':') {
            let port_str = &binding[colon_pos + 1..];
            port_str
                .parse::<u16>()
                .map_err(|_| CocoError::ConfigError(format!("无效的端口号: {}", port_str)))
        } else {
            Err(CocoError::ConfigError(format!(
                "无效的绑定地址格式: {}",
                binding
            )))
        }
    }

    // 辅助解析函数
    fn parse_log_level(level_str: &str) -> Result<Level> {
        match level_str.to_uppercase().as_str() {
            "TRACE" => Ok(Level::TRACE),
            "DEBUG" => Ok(Level::DEBUG),
            "INFO" => Ok(Level::INFO),
            "WARN" | "WARNING" => Ok(Level::WARN),
            "ERROR" => Ok(Level::ERROR),
            _ => Err(CocoError::ConfigError(format!(
                "无效的日志级别: {}",
                level_str
            ))),
        }
    }

    fn parse_log_format(format_str: &str) -> Result<LogFormat> {
        match format_str.to_lowercase().as_str() {
            "json" => Ok(LogFormat::Json),
            "pretty" => Ok(LogFormat::Pretty),
            "compact" => Ok(LogFormat::Compact),
            _ => Err(CocoError::ConfigError(format!(
                "无效的日志格式: {}",
                format_str
            ))),
        }
    }

    fn parse_log_output(output_str: &str) -> Result<LogOutput> {
        match output_str.to_lowercase().as_str() {
            "console" | "stdout" => Ok(LogOutput::Console),
            "file" => Ok(LogOutput::File),
            "both" | "all" => Ok(LogOutput::Both),
            _ => Err(CocoError::ConfigError(format!(
                "无效的日志输出目标: {}",
                output_str
            ))),
        }
    }
}
