/// 错误响应格式
/// 
/// 这个模块定义了统一的HTTP错误响应格式，确保所有API错误都以一致的格式返回给客户端。
/// 支持与Go版本API的兼容性，同时提供更丰富的错误信息。

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use super::{ApiError, AuthError, ServiceError, RepositoryError};

/// 统一错误响应结构
/// 
/// 提供标准化的错误响应格式，包含错误信息、状态码、时间戳等
#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorResponse {
    /// 错误消息（用户友好的描述）
    pub error: String,
    
    /// 详细错误信息（可选，用于调试）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message: Option<String>,
    
    /// HTTP状态码
    pub code: u16,
    
    /// 错误类型标识
    pub error_type: String,
    
    /// 错误发生时间戳
    pub timestamp: DateTime<Utc>,
    
    /// 错误追踪ID（用于日志关联）
    pub trace_id: String,
    
    /// 额外的错误详情（可选）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub details: Option<ErrorDetails>,
}

/// 错误详情结构
/// 
/// 提供更详细的错误上下文信息
#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorDetails {
    /// 相关字段（验证错误时使用）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub field: Option<String>,
    
    /// 错误值（验证错误时使用）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub value: Option<String>,
    
    /// 资源标识符（未找到错误时使用）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub resource_id: Option<String>,
    
    /// 重试建议（秒数）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub retry_after: Option<u64>,
    
    /// 支持的选项（如支持的媒体类型）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub supported_options: Option<Vec<String>>,
    
    /// 额外的上下文信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub context: Option<serde_json::Value>,
}

impl ErrorResponse {
    /// 从ApiError创建错误响应
    pub fn from_api_error(error: &ApiError) -> Self {
        let trace_id = Uuid::new_v4().to_string();
        let timestamp = Utc::now();
        let code = error.status_code().as_u16();
        let error_type = error.error_type().to_string();

        match error {
            ApiError::Service(service_err) => {
                Self::from_service_error(service_err, code, error_type, trace_id, timestamp)
            }
            ApiError::Auth(auth_err) => {
                Self::from_auth_error(auth_err, code, error_type, trace_id, timestamp)
            }
            ApiError::Validation { message, field, value } => {
                Self {
                    error: "请求验证失败".to_string(),
                    message: Some(message.clone()),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: Some(ErrorDetails {
                        field: field.clone(),
                        value: value.clone(),
                        ..Default::default()
                    }),
                }
            }
            ApiError::NotFound { resource, identifier } => {
                Self {
                    error: format!("资源未找到: {}", resource),
                    message: identifier.as_ref().map(|id| format!("标识符: {}", id)),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: Some(ErrorDetails {
                        resource_id: identifier.clone(),
                        ..Default::default()
                    }),
                }
            }
            ApiError::BadRequest { message, details } => {
                Self {
                    error: "请求格式错误".to_string(),
                    message: Some(message.clone()),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: details.as_ref().map(|d| ErrorDetails {
                        context: Some(serde_json::json!({"details": d})),
                        ..Default::default()
                    }),
                }
            }
            ApiError::Conflict { message, resource } => {
                Self {
                    error: "资源冲突".to_string(),
                    message: Some(message.clone()),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: resource.as_ref().map(|r| ErrorDetails {
                        context: Some(serde_json::json!({"resource": r})),
                        ..Default::default()
                    }),
                }
            }
            ApiError::TooManyRequests { retry_after } => {
                Self {
                    error: "请求过于频繁".to_string(),
                    message: Some("请稍后重试".to_string()),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: Some(ErrorDetails {
                        retry_after: *retry_after,
                        ..Default::default()
                    }),
                }
            }
            ApiError::InternalServerError { message, error_id } => {
                Self {
                    error: "内部服务器错误".to_string(),
                    message: Some(message.clone()),
                    code,
                    error_type,
                    timestamp,
                    trace_id: error_id.clone().unwrap_or(trace_id),
                    details: None,
                }
            }
            ApiError::ServiceUnavailable { message, retry_after } => {
                Self {
                    error: "服务不可用".to_string(),
                    message: Some(message.clone()),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: Some(ErrorDetails {
                        retry_after: *retry_after,
                        ..Default::default()
                    }),
                }
            }
            ApiError::RequestTimeout => {
                Self {
                    error: "请求超时".to_string(),
                    message: Some("请求处理时间过长".to_string()),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: None,
                }
            }
            ApiError::PayloadTooLarge { max_size, actual_size } => {
                Self {
                    error: format!("请求体过大，最大允许 {} 字节", max_size),
                    message: actual_size.map(|size| format!("实际大小: {} 字节", size)),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: Some(ErrorDetails {
                        context: Some(serde_json::json!({
                            "max_size": max_size,
                            "actual_size": actual_size
                        })),
                        ..Default::default()
                    }),
                }
            }
            ApiError::UnsupportedMediaType { media_type, supported_types } => {
                Self {
                    error: format!("不支持的媒体类型: {}", media_type),
                    message: Some("请使用支持的媒体类型".to_string()),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: Some(ErrorDetails {
                        supported_options: Some(supported_types.clone()),
                        ..Default::default()
                    }),
                }
            }
        }
    }

    /// 从ServiceError创建错误响应
    fn from_service_error(
        error: &ServiceError,
        code: u16,
        error_type: String,
        trace_id: String,
        timestamp: DateTime<Utc>,
    ) -> Self {
        match error {
            ServiceError::Repository(repo_err) => {
                Self::from_repository_error(repo_err, code, error_type, trace_id, timestamp)
            }
            ServiceError::BusinessLogic { message, code: biz_code, context } => {
                Self {
                    error: "业务逻辑错误".to_string(),
                    message: Some(message.clone()),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: Some(ErrorDetails {
                        context: Some(serde_json::json!({
                            "business_code": biz_code,
                            "context": context
                        })),
                        ..Default::default()
                    }),
                }
            }
            ServiceError::Validation { message, field, value } => {
                Self {
                    error: "数据验证失败".to_string(),
                    message: Some(message.clone()),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: Some(ErrorDetails {
                        field: field.clone(),
                        value: value.clone(),
                        ..Default::default()
                    }),
                }
            }
            ServiceError::Cache { message, operation, key } => {
                Self {
                    error: "缓存操作失败".to_string(),
                    message: Some(message.clone()),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: Some(ErrorDetails {
                        context: Some(serde_json::json!({
                            "operation": operation,
                            "key": key
                        })),
                        ..Default::default()
                    }),
                }
            }
            _ => {
                Self {
                    error: "服务错误".to_string(),
                    message: Some(error.to_string()),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: None,
                }
            }
        }
    }

    /// 从AuthError创建错误响应
    fn from_auth_error(
        error: &AuthError,
        code: u16,
        error_type: String,
        trace_id: String,
        timestamp: DateTime<Utc>,
    ) -> Self {
        let (error_msg, message) = match error {
            AuthError::InvalidCredentials => ("认证失败".to_string(), Some("用户名或密码错误".to_string())),
            AuthError::TokenExpired => ("令牌已过期".to_string(), Some("请重新登录".to_string())),
            AuthError::InvalidToken { reason } => ("无效令牌".to_string(), Some(reason.clone())),
            AuthError::MissingAuth { context } => ("缺少认证信息".to_string(), Some(context.clone())),
            AuthError::PermissionDenied { required_permission, .. } => {
                ("权限不足".to_string(), Some(format!("需要权限: {}", required_permission)))
            }
            AuthError::UserNotFound { user_identifier } => {
                ("用户不存在".to_string(), Some(format!("用户: {}", user_identifier)))
            }
            AuthError::UserDisabled { user_id } => {
                ("用户账户已被禁用".to_string(), Some(format!("用户ID: {}", user_id)))
            }
            AuthError::SessionExpired => ("会话已过期".to_string(), Some("请重新登录".to_string())),
            _ => ("认证错误".to_string(), Some(error.to_string())),
        };

        Self {
            error: error_msg,
            message,
            code,
            error_type,
            timestamp,
            trace_id,
            details: None,
        }
    }

    /// 从RepositoryError创建错误响应
    fn from_repository_error(
        error: &RepositoryError,
        code: u16,
        error_type: String,
        trace_id: String,
        timestamp: DateTime<Utc>,
    ) -> Self {
        match error {
            RepositoryError::NotFound { resource_type, identifier } => {
                Self {
                    error: format!("{}未找到", resource_type),
                    message: Some(format!("标识符: {}", identifier)),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: Some(ErrorDetails {
                        resource_id: Some(identifier.clone()),
                        ..Default::default()
                    }),
                }
            }
            RepositoryError::Conflict { message, field, value } => {
                Self {
                    error: "数据冲突".to_string(),
                    message: Some(message.clone()),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: Some(ErrorDetails {
                        field: field.clone(),
                        value: value.clone(),
                        ..Default::default()
                    }),
                }
            }
            _ => {
                Self {
                    error: "数据访问错误".to_string(),
                    message: Some(error.to_string()),
                    code,
                    error_type,
                    timestamp,
                    trace_id,
                    details: None,
                }
            }
        }
    }

    /// 创建简单的错误响应（用于向后兼容）
    pub fn simple(error: impl Into<String>, code: u16) -> Self {
        Self {
            error: error.into(),
            message: None,
            code,
            error_type: "unknown".to_string(),
            timestamp: Utc::now(),
            trace_id: Uuid::new_v4().to_string(),
            details: None,
        }
    }
}

impl Default for ErrorDetails {
    fn default() -> Self {
        Self {
            field: None,
            value: None,
            resource_id: None,
            retry_after: None,
            supported_options: None,
            context: None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::error::{ApiError, AuthError};

    #[test]
    fn test_error_response_creation() {
        let api_error = ApiError::validation("Invalid email", Some("email".to_string()), None);
        let response = ErrorResponse::from_api_error(&api_error);
        
        assert_eq!(response.error, "请求验证失败");
        assert_eq!(response.code, 400);
        assert_eq!(response.error_type, "validation");
        assert!(response.details.is_some());
        
        let details = response.details.unwrap();
        assert_eq!(details.field, Some("email".to_string()));
    }

    #[test]
    fn test_auth_error_response() {
        let auth_error = AuthError::TokenExpired;
        let api_error = ApiError::Auth(auth_error);
        let response = ErrorResponse::from_api_error(&api_error);
        
        assert_eq!(response.error, "令牌已过期");
        assert_eq!(response.code, 401);
        assert_eq!(response.error_type, "auth");
    }
}
