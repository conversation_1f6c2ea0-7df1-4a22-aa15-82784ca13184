/// 认证错误类型
/// 
/// 这个模块定义了认证和授权相关的错误类型，包括JWT验证、密码验证、令牌管理等错误。
/// 这些错误专门用于处理用户认证和权限验证场景。

use thiserror::Error;
use tracing::error;

/// 认证错误枚举
/// 
/// 定义了所有可能在认证和授权过程中发生的错误类型
#[derive(Debug, Error)]
pub enum AuthError {
    /// 无效凭据（用户名/密码错误）
    #[error("无效凭据")]
    InvalidCredentials,

    /// JWT令牌过期
    #[error("令牌已过期")]
    TokenExpired,

    /// 无效的JWT令牌
    #[error("无效令牌: {reason}")]
    InvalidToken { reason: String },

    /// 缺少认证信息
    #[error("缺少认证信息: {context}")]
    MissingAuth { context: String },

    /// 权限不足
    #[error("权限不足: {required_permission}")]
    PermissionDenied { 
        required_permission: String,
        user_permissions: Option<Vec<String>>,
    },

    /// JWT处理错误
    #[error("JWT处理错误: {message}")]
    JwtError { 
        message: String,
        #[source]
        source: Option<Box<dyn std::error::Error + Send + Sync>>,
    },

    /// 密码处理错误
    #[error("密码处理错误: {message}")]
    PasswordError { 
        message: String,
        #[source]
        source: Option<Box<dyn std::error::Error + Send + Sync>>,
    },

    /// 令牌管理错误
    #[error("令牌管理错误: {message}")]
    TokenManagement { 
        message: String,
        token_id: Option<String>,
    },

    /// 用户不存在
    #[error("用户不存在: {user_identifier}")]
    UserNotFound { user_identifier: String },

    /// 用户账户被禁用
    #[error("用户账户已被禁用: {user_id}")]
    UserDisabled { user_id: String },

    /// 会话过期
    #[error("会话已过期")]
    SessionExpired,

    /// 认证配置错误
    #[error("认证配置错误: {message}")]
    ConfigurationError { 
        message: String,
        config_key: Option<String>,
    },

    /// 数据库错误（认证相关）
    #[error("认证数据库错误: {message}")]
    Database { 
        message: String,
        operation: Option<String>,
    },

    /// 外部认证提供商错误
    #[error("外部认证错误: {provider} - {message}")]
    ExternalProvider { 
        provider: String,
        message: String,
        error_code: Option<String>,
    },
}

impl AuthError {
    /// 创建无效令牌错误
    pub fn invalid_token(reason: impl Into<String>) -> Self {
        let error = Self::InvalidToken { 
            reason: reason.into() 
        };
        error.log_error();
        error
    }

    /// 创建缺少认证错误
    pub fn missing_auth(context: impl Into<String>) -> Self {
        let error = Self::MissingAuth { 
            context: context.into() 
        };
        error.log_error();
        error
    }

    /// 创建权限不足错误
    pub fn permission_denied(
        required_permission: impl Into<String>, 
        user_permissions: Option<Vec<String>>
    ) -> Self {
        let error = Self::PermissionDenied {
            required_permission: required_permission.into(),
            user_permissions,
        };
        error.log_error();
        error
    }

    /// 创建JWT错误
    pub fn jwt_error<E>(message: impl Into<String>, source: Option<E>) -> Self 
    where 
        E: std::error::Error + Send + Sync + 'static 
    {
        let error = Self::JwtError {
            message: message.into(),
            source: source.map(|e| Box::new(e) as Box<dyn std::error::Error + Send + Sync>),
        };
        error.log_error();
        error
    }

    /// 创建密码错误
    pub fn password_error<E>(message: impl Into<String>, source: Option<E>) -> Self 
    where 
        E: std::error::Error + Send + Sync + 'static 
    {
        let error = Self::PasswordError {
            message: message.into(),
            source: source.map(|e| Box::new(e) as Box<dyn std::error::Error + Send + Sync>),
        };
        error.log_error();
        error
    }

    /// 创建令牌管理错误
    pub fn token_management(message: impl Into<String>, token_id: Option<String>) -> Self {
        let error = Self::TokenManagement {
            message: message.into(),
            token_id,
        };
        error.log_error();
        error
    }

    /// 创建用户不存在错误
    pub fn user_not_found(user_identifier: impl Into<String>) -> Self {
        let error = Self::UserNotFound { 
            user_identifier: user_identifier.into() 
        };
        error.log_error();
        error
    }

    /// 创建用户禁用错误
    pub fn user_disabled(user_id: impl Into<String>) -> Self {
        let error = Self::UserDisabled { 
            user_id: user_id.into() 
        };
        error.log_error();
        error
    }

    /// 创建配置错误
    pub fn configuration_error(message: impl Into<String>, config_key: Option<String>) -> Self {
        let error = Self::ConfigurationError {
            message: message.into(),
            config_key,
        };
        error.log_error();
        error
    }

    /// 创建数据库错误
    pub fn database(message: impl Into<String>, operation: Option<String>) -> Self {
        let error = Self::Database {
            message: message.into(),
            operation,
        };
        error.log_error();
        error
    }

    /// 创建外部提供商错误
    pub fn external_provider(
        provider: impl Into<String>, 
        message: impl Into<String>, 
        error_code: Option<String>
    ) -> Self {
        let error = Self::ExternalProvider {
            provider: provider.into(),
            message: message.into(),
            error_code,
        };
        error.log_error();
        error
    }

    /// 记录错误日志
    fn log_error(&self) {
        match self {
            Self::InvalidCredentials => {
                error!(
                    error_type = "auth_invalid_credentials",
                    "用户凭据验证失败"
                );
            }
            Self::TokenExpired => {
                error!(
                    error_type = "auth_token_expired",
                    "令牌已过期"
                );
            }
            Self::InvalidToken { reason } => {
                error!(
                    error_type = "auth_invalid_token",
                    reason = %reason,
                    "无效令牌"
                );
            }
            Self::MissingAuth { context } => {
                error!(
                    error_type = "auth_missing",
                    context = %context,
                    "缺少认证信息"
                );
            }
            Self::PermissionDenied { required_permission, user_permissions } => {
                error!(
                    error_type = "auth_permission_denied",
                    required_permission = %required_permission,
                    user_permissions = ?user_permissions,
                    "权限验证失败"
                );
            }
            Self::JwtError { message, .. } => {
                error!(
                    error_type = "auth_jwt_error",
                    message = %message,
                    "JWT处理失败"
                );
            }
            Self::PasswordError { message, .. } => {
                error!(
                    error_type = "auth_password_error",
                    message = %message,
                    "密码处理失败"
                );
            }
            Self::TokenManagement { message, token_id } => {
                error!(
                    error_type = "auth_token_management",
                    message = %message,
                    token_id = ?token_id,
                    "令牌管理失败"
                );
            }
            Self::UserNotFound { user_identifier } => {
                error!(
                    error_type = "auth_user_not_found",
                    user_identifier = %user_identifier,
                    "用户不存在"
                );
            }
            Self::UserDisabled { user_id } => {
                error!(
                    error_type = "auth_user_disabled",
                    user_id = %user_id,
                    "用户账户被禁用"
                );
            }
            Self::SessionExpired => {
                error!(
                    error_type = "auth_session_expired",
                    "会话已过期"
                );
            }
            Self::ConfigurationError { message, config_key } => {
                error!(
                    error_type = "auth_configuration_error",
                    message = %message,
                    config_key = ?config_key,
                    "认证配置错误"
                );
            }
            Self::Database { message, operation } => {
                error!(
                    error_type = "auth_database_error",
                    message = %message,
                    operation = ?operation,
                    "认证数据库错误"
                );
            }
            Self::ExternalProvider { provider, message, error_code } => {
                error!(
                    error_type = "auth_external_provider",
                    provider = %provider,
                    message = %message,
                    error_code = ?error_code,
                    "外部认证提供商错误"
                );
            }
        }
    }

    /// 获取错误类型字符串
    pub fn error_type(&self) -> &'static str {
        match self {
            Self::InvalidCredentials => "invalid_credentials",
            Self::TokenExpired => "token_expired",
            Self::InvalidToken { .. } => "invalid_token",
            Self::MissingAuth { .. } => "missing_auth",
            Self::PermissionDenied { .. } => "permission_denied",
            Self::JwtError { .. } => "jwt_error",
            Self::PasswordError { .. } => "password_error",
            Self::TokenManagement { .. } => "token_management",
            Self::UserNotFound { .. } => "user_not_found",
            Self::UserDisabled { .. } => "user_disabled",
            Self::SessionExpired => "session_expired",
            Self::ConfigurationError { .. } => "configuration_error",
            Self::Database { .. } => "database",
            Self::ExternalProvider { .. } => "external_provider",
        }
    }

    /// 检查是否是客户端错误（用户可以修正的错误）
    pub fn is_client_error(&self) -> bool {
        matches!(self, 
            Self::InvalidCredentials |
            Self::TokenExpired |
            Self::InvalidToken { .. } |
            Self::MissingAuth { .. } |
            Self::PermissionDenied { .. } |
            Self::UserNotFound { .. } |
            Self::UserDisabled { .. } |
            Self::SessionExpired
        )
    }

    /// 检查是否需要重新认证
    pub fn requires_reauth(&self) -> bool {
        matches!(self, 
            Self::TokenExpired |
            Self::InvalidToken { .. } |
            Self::SessionExpired
        )
    }
}

// 从JWT相关错误转换
impl From<jsonwebtoken::errors::Error> for AuthError {
    fn from(err: jsonwebtoken::errors::Error) -> Self {
        use jsonwebtoken::errors::ErrorKind;
        
        match err.kind() {
            ErrorKind::ExpiredSignature => Self::TokenExpired,
            ErrorKind::InvalidToken => Self::invalid_token("Invalid JWT token"),
            ErrorKind::InvalidSignature => Self::invalid_token("Invalid JWT signature"),
            _ => Self::jwt_error(format!("JWT error: {}", err), Some(err)),
        }
    }
}

// 从bcrypt错误转换
impl From<bcrypt::BcryptError> for AuthError {
    fn from(err: bcrypt::BcryptError) -> Self {
        Self::password_error(format!("Bcrypt error: {}", err), Some(err))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_auth_error_creation() {
        let invalid_token = AuthError::invalid_token("Malformed token");
        assert!(matches!(invalid_token, AuthError::InvalidToken { .. }));
        assert_eq!(invalid_token.error_type(), "invalid_token");
        assert!(invalid_token.is_client_error());
        assert!(invalid_token.requires_reauth());

        let permission_denied = AuthError::permission_denied(
            "admin", 
            Some(vec!["user".to_string()])
        );
        assert!(matches!(permission_denied, AuthError::PermissionDenied { .. }));
        assert!(permission_denied.is_client_error());
        assert!(!permission_denied.requires_reauth());
    }

    #[test]
    fn test_error_classification() {
        let token_expired = AuthError::TokenExpired;
        assert!(token_expired.is_client_error());
        assert!(token_expired.requires_reauth());

        let config_error = AuthError::configuration_error("Missing JWT secret", None);
        assert!(!config_error.is_client_error());
        assert!(!config_error.requires_reauth());
    }
}
