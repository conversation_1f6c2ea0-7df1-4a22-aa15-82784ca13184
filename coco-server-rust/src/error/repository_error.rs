/// 仓储层错误类型
/// 
/// 这个模块定义了数据访问层的错误类型，包括数据库操作、查询和序列化错误。
/// 这是错误处理系统的最底层，其他层的错误可以包装这些错误。

use thiserror::Error;
use tracing::error;

/// 仓储层错误枚举
/// 
/// 定义了所有可能在数据访问层发生的错误类型
#[derive(Debug, Error)]
pub enum RepositoryError {
    /// 数据库连接或操作错误
    #[error("数据库错误: {message}")]
    Database { 
        message: String,
        #[source]
        source: Option<Box<dyn std::error::Error + Send + Sync>>,
    },

    /// 查询执行错误
    #[error("查询错误: {message}")]
    Query { 
        message: String,
        query: Option<String>,
    },

    /// 数据序列化/反序列化错误
    #[error("序列化错误: {message}")]
    Serialization { 
        message: String,
        #[source]
        source: Option<Box<dyn std::error::Error + Send + Sync>>,
    },

    /// 资源未找到错误
    #[error("资源未找到: {resource_type} with {identifier}")]
    NotFound { 
        resource_type: String,
        identifier: String,
    },

    /// 资源冲突错误（如唯一性约束违反）
    #[error("资源冲突: {message}")]
    Conflict { 
        message: String,
        field: Option<String>,
        value: Option<String>,
    },

    /// 连接错误
    #[error("连接错误: {message}")]
    Connection { 
        message: String,
        #[source]
        source: Option<Box<dyn std::error::Error + Send + Sync>>,
    },

    /// 事务错误
    #[error("事务错误: {message}")]
    Transaction { 
        message: String,
        operation: Option<String>,
    },

    /// 验证错误
    #[error("数据验证错误: {message}")]
    Validation { 
        message: String,
        field: Option<String>,
    },
}

impl RepositoryError {
    /// 创建数据库错误
    pub fn database<E>(message: impl Into<String>, source: Option<E>) -> Self 
    where 
        E: std::error::Error + Send + Sync + 'static 
    {
        let error = Self::Database {
            message: message.into(),
            source: source.map(|e| Box::new(e) as Box<dyn std::error::Error + Send + Sync>),
        };
        error.log_error();
        error
    }

    /// 创建查询错误
    pub fn query(message: impl Into<String>, query: Option<String>) -> Self {
        let error = Self::Query {
            message: message.into(),
            query,
        };
        error.log_error();
        error
    }

    /// 创建序列化错误
    pub fn serialization<E>(message: impl Into<String>, source: Option<E>) -> Self 
    where 
        E: std::error::Error + Send + Sync + 'static 
    {
        let error = Self::Serialization {
            message: message.into(),
            source: source.map(|e| Box::new(e) as Box<dyn std::error::Error + Send + Sync>),
        };
        error.log_error();
        error
    }

    /// 创建未找到错误
    pub fn not_found(resource_type: impl Into<String>, identifier: impl Into<String>) -> Self {
        let error = Self::NotFound {
            resource_type: resource_type.into(),
            identifier: identifier.into(),
        };
        error.log_error();
        error
    }

    /// 创建冲突错误
    pub fn conflict(
        message: impl Into<String>, 
        field: Option<String>, 
        value: Option<String>
    ) -> Self {
        let error = Self::Conflict {
            message: message.into(),
            field,
            value,
        };
        error.log_error();
        error
    }

    /// 创建连接错误
    pub fn connection<E>(message: impl Into<String>, source: Option<E>) -> Self 
    where 
        E: std::error::Error + Send + Sync + 'static 
    {
        let error = Self::Connection {
            message: message.into(),
            source: source.map(|e| Box::new(e) as Box<dyn std::error::Error + Send + Sync>),
        };
        error.log_error();
        error
    }

    /// 创建事务错误
    pub fn transaction(message: impl Into<String>, operation: Option<String>) -> Self {
        let error = Self::Transaction {
            message: message.into(),
            operation,
        };
        error.log_error();
        error
    }

    /// 创建验证错误
    pub fn validation(message: impl Into<String>, field: Option<String>) -> Self {
        let error = Self::Validation {
            message: message.into(),
            field,
        };
        error.log_error();
        error
    }

    /// 记录错误日志
    fn log_error(&self) {
        match self {
            Self::Database { message, .. } => {
                error!(
                    error_type = "repository_database",
                    message = %message,
                    "数据库操作失败"
                );
            }
            Self::Query { message, query } => {
                error!(
                    error_type = "repository_query",
                    message = %message,
                    query = ?query,
                    "查询执行失败"
                );
            }
            Self::Serialization { message, .. } => {
                error!(
                    error_type = "repository_serialization",
                    message = %message,
                    "数据序列化失败"
                );
            }
            Self::NotFound { resource_type, identifier } => {
                error!(
                    error_type = "repository_not_found",
                    resource_type = %resource_type,
                    identifier = %identifier,
                    "资源未找到"
                );
            }
            Self::Conflict { message, field, value } => {
                error!(
                    error_type = "repository_conflict",
                    message = %message,
                    field = ?field,
                    value = ?value,
                    "资源冲突"
                );
            }
            Self::Connection { message, .. } => {
                error!(
                    error_type = "repository_connection",
                    message = %message,
                    "连接失败"
                );
            }
            Self::Transaction { message, operation } => {
                error!(
                    error_type = "repository_transaction",
                    message = %message,
                    operation = ?operation,
                    "事务失败"
                );
            }
            Self::Validation { message, field } => {
                error!(
                    error_type = "repository_validation",
                    message = %message,
                    field = ?field,
                    "数据验证失败"
                );
            }
        }
    }

    /// 获取错误类型字符串
    pub fn error_type(&self) -> &'static str {
        match self {
            Self::Database { .. } => "database",
            Self::Query { .. } => "query",
            Self::Serialization { .. } => "serialization",
            Self::NotFound { .. } => "not_found",
            Self::Conflict { .. } => "conflict",
            Self::Connection { .. } => "connection",
            Self::Transaction { .. } => "transaction",
            Self::Validation { .. } => "validation",
        }
    }

    /// 检查是否是可重试的错误
    pub fn is_retryable(&self) -> bool {
        matches!(self, 
            Self::Connection { .. } | 
            Self::Database { .. } |
            Self::Transaction { .. }
        )
    }
}

// 从SurrealDB错误转换
impl From<surrealdb::Error> for RepositoryError {
    fn from(err: surrealdb::Error) -> Self {
        Self::database(format!("SurrealDB error: {}", err), Some(err))
    }
}

// 从序列化错误转换
impl From<serde_json::Error> for RepositoryError {
    fn from(err: serde_json::Error) -> Self {
        Self::serialization(format!("JSON serialization error: {}", err), Some(err))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_repository_error_creation() {
        let db_error = RepositoryError::database("Connection failed", None::<std::io::Error>);
        assert!(matches!(db_error, RepositoryError::Database { .. }));
        assert_eq!(db_error.error_type(), "database");
        assert!(db_error.is_retryable());

        let not_found_error = RepositoryError::not_found("ModelProvider", "test-id");
        assert!(matches!(not_found_error, RepositoryError::NotFound { .. }));
        assert_eq!(not_found_error.error_type(), "not_found");
        assert!(!not_found_error.is_retryable());
    }

    #[test]
    fn test_error_conversion() {
        let json_error = serde_json::from_str::<serde_json::Value>("invalid json");
        assert!(json_error.is_err());
        
        let repo_error: RepositoryError = json_error.unwrap_err().into();
        assert!(matches!(repo_error, RepositoryError::Serialization { .. }));
    }
}
