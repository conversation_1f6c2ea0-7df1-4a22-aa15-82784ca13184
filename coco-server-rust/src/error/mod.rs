pub mod error;
pub mod result;

// 新的分层错误系统
pub mod api_error;
pub mod auth_error;
pub mod repository_error;
pub mod response;
pub mod service_error;

// 保持向后兼容性
// 导出新的错误类型
pub use api_error::ApiError;
pub use auth_error::AuthError;
pub use error::CocoError as Error;
pub use repository_error::RepositoryError;
pub use response::ErrorResponse;
pub use result::Result;
pub use service_error::ServiceError;
