use crate::database::DB;
use crate::error::Result;
use tracing::{info, warn};

/// 数据库Schema初始化器
/// 
/// 用于全新项目的数据库表结构初始化，不涉及数据迁移
pub struct SchemaInitializer;

impl SchemaInitializer {
    /// 初始化所有数据库表结构
    pub async fn initialize_all() -> Result<()> {
        info!("🚀 开始初始化数据库Schema...");

        // 初始化核心表
        Self::init_core_tables().await?;
        
        // 初始化认证相关表
        Self::init_auth_tables().await?;
        
        // 初始化索引
        Self::init_indexes().await?;

        info!("✅ 数据库Schema初始化完成");
        Ok(())
    }

    /// 初始化核心业务表
    async fn init_core_tables() -> Result<()> {
        info!("📊 初始化核心业务表...");

        // DataSource表 - 数据源管理
        DB.query(r#"
            DEFINE TABLE IF NOT EXISTS datasource SCHEMALESS;
        "#).await.map_err(|e| crate::error::Error::Database(format!("创建datasource表失败: {}", e)))?;

        // Document表 - 文档管理
        DB.query(r#"
            DEFINE TABLE IF NOT EXISTS document SCHEMALESS;
        "#).await.map_err(|e| crate::error::Error::Database(format!("创建document表失败: {}", e)))?;

        // ModelProvider表 - 模型提供商
        DB.query(r#"
            DEFINE TABLE IF NOT EXISTS model_provider SCHEMALESS;
        "#).await.map_err(|e| crate::error::Error::Database(format!("创建model_provider表失败: {}", e)))?;

        info!("✅ 核心业务表初始化完成");
        Ok(())
    }

    /// 初始化认证相关表
    async fn init_auth_tables() -> Result<()> {
        info!("🔐 初始化认证相关表...");

        // User表 - 用户管理
        DB.query(r#"
            DEFINE TABLE IF NOT EXISTS user SCHEMALESS;
        "#).await.map_err(|e| crate::error::Error::Database(format!("创建user表失败: {}", e)))?;

        // AccessTokens表 - API令牌管理
        DB.query(r#"
            DEFINE TABLE IF NOT EXISTS access_tokens SCHEMALESS;
        "#).await.map_err(|e| crate::error::Error::Database(format!("创建access_tokens表失败: {}", e)))?;

        info!("✅ 认证相关表初始化完成");
        Ok(())
    }

    /// 初始化索引
    async fn init_indexes() -> Result<()> {
        info!("📇 初始化数据库索引...");

        // 为access_tokens表创建索引
        DB.query(r#"
            DEFINE INDEX IF NOT EXISTS idx_access_token 
            ON TABLE access_tokens COLUMNS access_token UNIQUE;
        "#).await.map_err(|e| {
            warn!("创建access_token索引失败: {}", e);
            crate::error::Error::Database(format!("创建索引失败: {}", e))
        })?;

        DB.query(r#"
            DEFINE INDEX IF NOT EXISTS idx_user_tokens 
            ON TABLE access_tokens COLUMNS user_id;
        "#).await.map_err(|e| {
            warn!("创建user_tokens索引失败: {}", e);
            crate::error::Error::Database(format!("创建索引失败: {}", e))
        })?;

        // 为datasource表创建索引
        DB.query(r#"
            DEFINE INDEX IF NOT EXISTS idx_datasource_name 
            ON TABLE datasource COLUMNS name;
        "#).await.map_err(|e| {
            warn!("创建datasource_name索引失败: {}", e);
            crate::error::Error::Database(format!("创建索引失败: {}", e))
        })?;

        info!("✅ 数据库索引初始化完成");
        Ok(())
    }

    /// 检查数据库连接状态
    pub async fn check_connection() -> Result<()> {
        info!("🔍 检查数据库连接状态...");
        
        match DB.query("INFO FOR DB").await {
            Ok(_) => {
                info!("✅ 数据库连接正常");
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("数据库连接检查失败: {}", e);
                warn!("{}", error_msg);
                Err(crate::error::Error::Database(error_msg))
            }
        }
    }

    /// 获取数据库信息
    pub async fn get_database_info() -> Result<serde_json::Value> {
        let mut response = DB.query("INFO FOR DB").await
            .map_err(|e| crate::error::Error::Database(format!("获取数据库信息失败: {}", e)))?;
        
        let info: Option<serde_json::Value> = response.take(0)
            .map_err(|e| crate::error::Error::Database(format!("解析数据库信息失败: {}", e)))?;
        
        info.ok_or_else(|| crate::error::Error::Database("未获取到数据库信息".to_string()))
    }

    /// 清理所有表（仅用于开发/测试环境）
    #[cfg(debug_assertions)]
    pub async fn cleanup_all_tables() -> Result<()> {
        warn!("⚠️ 清理所有数据库表（仅开发模式）...");
        
        let tables = ["datasource", "document", "model_provider", "user", "access_tokens"];
        
        for table in &tables {
            if let Err(e) = DB.query(&format!("REMOVE TABLE IF EXISTS {}", table)).await {
                warn!("清理表 {} 失败: {}", table, e);
            } else {
                info!("✅ 已清理表: {}", table);
            }
        }
        
        info!("✅ 数据库表清理完成");
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    #[ignore] // 需要运行中的SurrealDB实例
    async fn test_schema_initialization() {
        let result = SchemaInitializer::initialize_all().await;
        assert!(result.is_ok(), "Schema初始化应该成功");
    }

    #[tokio::test]
    #[ignore] // 需要运行中的SurrealDB实例
    async fn test_connection_check() {
        let result = SchemaInitializer::check_connection().await;
        assert!(result.is_ok(), "数据库连接检查应该成功");
    }
}
