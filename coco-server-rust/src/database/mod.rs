// SurrealDB数据库模块
pub mod client;
pub mod config;
pub mod schema;

pub use client::SurrealDBClient;
pub use config::DatabaseConfig;

// 全局数据库连接（参考SurrealDB官方文档）
use std::sync::LazyLock;
use surrealdb::{
    engine::remote::ws::{Client, Ws},
    opt::auth::Root,
    Surreal,
};

/// 全局数据库实例
pub static DB: LazyLock<Surreal<Client>> = LazyLock::new(Surreal::init);

/// 初始化全局数据库连接
pub async fn init_database(config: &DatabaseConfig) -> crate::error::Result<()> {
    // 连接到数据库
    DB.connect::<Ws>(&config.url)
        .await
        .map_err(|e| crate::error::Error::Database(format!("连接SurrealDB失败: {}", e)))?;

    // 尝试使用root用户登录（如果失败则跳过认证）
    if let Err(e) = DB
        .signin(Root {
            username: &config.username,
            password: &config.password,
        })
        .await
    {
        tracing::warn!("SurrealDB登录失败，跳过认证: {}", e);
        // 继续执行，不返回错误
    }

    // 选择命名空间和数据库
    DB.use_ns(&config.namespace)
        .use_db(&config.database)
        .await
        .map_err(|e| crate::error::Error::Database(format!("选择数据库失败: {}", e)))?;

    tracing::info!("全局数据库连接初始化成功");
    Ok(())
}
