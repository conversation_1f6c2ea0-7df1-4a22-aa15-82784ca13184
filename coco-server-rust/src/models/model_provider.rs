use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use validator::Validate;

/// Model Provider 主要数据结构
/// 对应Go版本的common.ModelProvider
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelProvider {
    pub id: String,
    pub created: DateTime<Utc>,
    pub updated: DateTime<Utc>,
    pub name: String,
    pub api_key: String,
    pub api_type: String,
    pub base_url: String,
    pub icon: String,
    pub models: Vec<ModelConfig>,
    pub enabled: bool,
    pub builtin: bool,
    pub description: String,
}

/// 模型配置结构
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct ModelConfig {
    #[validate(length(min = 1, max = 100, message = "模型名称长度必须在1-100字符之间"))]
    pub name: String,

    #[serde(skip_serializing_if = "Option::is_none")]
    #[validate]
    pub settings: Option<ModelSettings>,
}

/// 模型设置参数
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Validate)]
pub struct ModelSettings {
    #[serde(skip_serializing_if = "Option::is_none")]
    #[validate(range(min = 0.0, max = 2.0, message = "温度值必须在0.0-2.0之间"))]
    pub temperature: Option<f64>,

    #[serde(skip_serializing_if = "Option::is_none")]
    #[validate(range(min = 0.0, max = 1.0, message = "top_p值必须在0.0-1.0之间"))]
    pub top_p: Option<f64>,

    #[serde(skip_serializing_if = "Option::is_none")]
    #[validate(range(min = -2.0, max = 2.0, message = "presence_penalty值必须在-2.0-2.0之间"))]
    pub presence_penalty: Option<f64>,

    #[serde(skip_serializing_if = "Option::is_none")]
    #[validate(range(min = -2.0, max = 2.0, message = "frequency_penalty值必须在-2.0-2.0之间"))]
    pub frequency_penalty: Option<f64>,

    #[serde(skip_serializing_if = "Option::is_none")]
    #[validate(range(min = 1, max = 32768, message = "max_tokens值必须在1-32768之间"))]
    pub max_tokens: Option<i32>,
}

/// 创建模型提供商请求
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct CreateModelProviderRequest {
    #[validate(length(min = 1, max = 100, message = "名称长度必须在1-100字符之间"))]
    pub name: String,

    #[validate(length(min = 1, message = "API密钥不能为空"))]
    pub api_key: String,

    #[validate(custom = "crate::models::validation::validators::validate_api_type")]
    pub api_type: String,

    #[validate(url(message = "请提供有效的URL"))]
    pub base_url: String,

    #[serde(default)]
    #[validate(length(max = 200, message = "图标URL长度不能超过200字符"))]
    pub icon: String,

    #[serde(default)]
    #[validate]
    pub models: Vec<ModelConfig>,

    #[serde(default = "default_enabled")]
    pub enabled: bool,

    #[serde(default)]
    #[validate(length(max = 500, message = "描述长度不能超过500字符"))]
    pub description: String,
}

/// 更新模型提供商请求
#[derive(Debug, Deserialize, Validate)]
pub struct UpdateModelProviderRequest {
    #[validate(length(min = 1, max = 100, message = "名称长度必须在1-100字符之间"))]
    pub name: Option<String>,

    #[validate(length(min = 1, message = "API密钥不能为空"))]
    pub api_key: Option<String>,

    #[validate(custom = "crate::models::validation::validators::validate_api_type")]
    pub api_type: Option<String>,

    #[validate(url(message = "请提供有效的URL"))]
    pub base_url: Option<String>,

    #[validate(length(max = 200, message = "图标URL长度不能超过200字符"))]
    pub icon: Option<String>,

    #[validate]
    pub models: Option<Vec<ModelConfig>>,

    pub enabled: Option<bool>,

    #[validate(length(max = 500, message = "描述长度不能超过500字符"))]
    pub description: Option<String>,
}

/// API响应结构，兼容Go版本格式
#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    #[serde(rename = "_id")]
    pub id: String,
    pub result: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,
}

/// 搜索查询参数
#[derive(Debug, Deserialize)]
pub struct SearchParams {
    #[serde(default)]
    pub size: Option<usize>,
    #[serde(default)]
    pub from: Option<usize>,
    #[serde(default)]
    pub sort: Option<String>,
    #[serde(default)]
    pub q: Option<String>, // 查询字符串
}

/// 搜索响应结构，兼容Elasticsearch格式
#[derive(Debug, Serialize)]
pub struct ModelProviderSearchResponse {
    pub took: u64,
    pub timed_out: bool,
    pub hits: ModelProviderSearchHits,
}

#[derive(Debug, Serialize)]
pub struct ModelProviderSearchHits {
    pub total: ModelProviderSearchTotal,
    pub max_score: Option<f64>,
    pub hits: Vec<ModelProviderSearchHit>,
}

#[derive(Debug, Serialize)]
pub struct ModelProviderSearchTotal {
    pub value: usize,
    pub relation: String,
}

#[derive(Debug, Serialize)]
pub struct ModelProviderSearchHit {
    #[serde(rename = "_index")]
    pub index: String,
    #[serde(rename = "_type")]
    pub doc_type: String,
    #[serde(rename = "_id")]
    pub id: String,
    #[serde(rename = "_score")]
    pub score: Option<f64>,
    #[serde(rename = "_source")]
    pub source: ModelProvider,
}

// 默认值函数
fn default_enabled() -> bool {
    true
}

// 工具函数
/// 遮蔽API密钥，只显示前4位和后4位
fn mask_api_key(api_key: &str) -> String {
    if api_key.len() <= 8 {
        "*".repeat(api_key.len())
    } else {
        let start = &api_key[..4];
        let end = &api_key[api_key.len() - 4..];
        let middle = "*".repeat(api_key.len() - 8);
        format!("{}{}{}", start, middle, end)
    }
}

impl ModelProvider {
    /// 创建新的ModelProvider实例
    pub fn new(req: CreateModelProviderRequest) -> Self {
        let now = Utc::now();
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            created: now,
            updated: now,
            name: req.name,
            api_key: req.api_key,
            api_type: req.api_type,
            base_url: req.base_url,
            icon: req.icon,
            models: req.models,
            enabled: req.enabled,
            builtin: false, // 新创建的都不是内置的
            description: req.description,
        }
    }

    /// 更新ModelProvider
    pub fn update(&mut self, req: UpdateModelProviderRequest) {
        if let Some(name) = req.name {
            self.name = name;
        }
        if let Some(api_key) = req.api_key {
            self.api_key = api_key;
        }
        if let Some(api_type) = req.api_type {
            self.api_type = api_type;
        }
        if let Some(base_url) = req.base_url {
            self.base_url = base_url;
        }
        if let Some(icon) = req.icon {
            self.icon = icon;
        }
        if let Some(models) = req.models {
            self.models = models;
        }
        if let Some(enabled) = req.enabled {
            self.enabled = enabled;
        }
        if let Some(description) = req.description {
            self.description = description;
        }
        self.updated = Utc::now();
    }

    /// 过滤敏感字段，用于API响应
    ///
    /// # 参数
    /// * `include_sensitive` -
    ///   是否包含敏感信息（如API密钥），通常只有管理员才能看到
    pub fn sanitize(&self, include_sensitive: bool) -> serde_json::Value {
        let mut value = serde_json::to_value(self).unwrap();
        if let Some(obj) = value.as_object_mut() {
            if !include_sensitive {
                // 移除敏感字段
                obj.remove("api_key");
            } else {
                // 即使包含敏感信息，也要部分遮蔽API密钥
                if let Some(api_key) = obj.get_mut("api_key") {
                    if let Some(key_str) = api_key.as_str() {
                        let masked_key = mask_api_key(key_str);
                        *api_key = serde_json::Value::String(masked_key);
                    }
                }
            }
        }
        value
    }

    /// 获取完全清理的版本（用于普通用户）
    pub fn sanitize_for_user(&self) -> serde_json::Value {
        self.sanitize(false)
    }

    /// 获取管理员版本（包含部分遮蔽的敏感信息）
    pub fn sanitize_for_admin(&self) -> serde_json::Value {
        self.sanitize(true)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_model_provider_creation() {
        let req = CreateModelProviderRequest {
            name: "Test Provider".to_string(),
            api_key: "test-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.test.com".to_string(),
            icon: "test-icon".to_string(),
            models: vec![],
            enabled: true,
            description: "Test description".to_string(),
        };

        let provider = ModelProvider::new(req);
        assert_eq!(provider.name, "Test Provider");
        assert_eq!(provider.api_type, "openai");
        assert!(!provider.builtin);
        assert!(provider.enabled);
    }

    #[test]
    fn test_model_provider_update() {
        let req = CreateModelProviderRequest {
            name: "Test Provider".to_string(),
            api_key: "test-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.test.com".to_string(),
            icon: "test-icon".to_string(),
            models: vec![],
            enabled: true,
            description: "Test description".to_string(),
        };

        let mut provider = ModelProvider::new(req);
        let original_updated = provider.updated;

        // 等待一毫秒确保时间戳不同
        std::thread::sleep(std::time::Duration::from_millis(1));

        let update_req = UpdateModelProviderRequest {
            name: Some("Updated Provider".to_string()),
            api_key: None,
            api_type: None,
            base_url: None,
            icon: None,
            models: None,
            enabled: Some(false),
            description: None,
        };

        provider.update(update_req);
        assert_eq!(provider.name, "Updated Provider");
        assert!(!provider.enabled);
        assert!(provider.updated > original_updated);
    }

    #[test]
    fn test_serialization() {
        let provider = ModelProvider {
            id: "test-id".to_string(),
            created: Utc::now(),
            updated: Utc::now(),
            name: "Test Provider".to_string(),
            api_key: "test-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.test.com".to_string(),
            icon: "test-icon".to_string(),
            models: vec![ModelConfig {
                name: "test-model".to_string(),
                settings: Some(ModelSettings {
                    temperature: Some(0.8),
                    top_p: Some(0.9),
                    presence_penalty: None,
                    frequency_penalty: None,
                    max_tokens: Some(1024),
                }),
            }],
            enabled: true,
            builtin: false,
            description: "Test description".to_string(),
        };

        // 测试序列化
        let json = serde_json::to_string(&provider).unwrap();
        assert!(json.contains("Test Provider"));

        // 测试反序列化
        let deserialized: ModelProvider = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.name, provider.name);
        assert_eq!(deserialized.models.len(), 1);
    }

    #[test]
    fn test_sanitize_for_user() {
        let provider = ModelProvider {
            id: "test-id".to_string(),
            created: Utc::now(),
            updated: Utc::now(),
            name: "Test Provider".to_string(),
            api_key: "secret-api-key-12345".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.test.com".to_string(),
            icon: "test-icon".to_string(),
            models: vec![],
            enabled: true,
            builtin: false,
            description: "Test description".to_string(),
        };

        let sanitized = provider.sanitize_for_user();

        // API密钥应该被完全移除
        assert!(!sanitized.to_string().contains("secret-api-key"));
        assert!(sanitized.get("name").unwrap().as_str().unwrap() == "Test Provider");
        assert!(sanitized.get("api_key").is_none());
    }

    #[test]
    fn test_sanitize_for_admin() {
        let provider = ModelProvider {
            id: "test-id".to_string(),
            created: Utc::now(),
            updated: Utc::now(),
            name: "Test Provider".to_string(),
            api_key: "secret-api-key-12345".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.test.com".to_string(),
            icon: "test-icon".to_string(),
            models: vec![],
            enabled: true,
            builtin: false,
            description: "Test description".to_string(),
        };

        let sanitized = provider.sanitize_for_admin();

        // API密钥应该被部分遮蔽
        let api_key = sanitized.get("api_key").unwrap().as_str().unwrap();
        assert!(api_key.starts_with("secr"));
        assert!(api_key.ends_with("2345"));
        assert!(api_key.contains("*"));
        assert_ne!(api_key, "secret-api-key-12345");
    }

    #[test]
    fn test_mask_api_key() {
        // 测试长密钥 (19个字符: sk-1234567890abcdef)
        // 前4位: sk-1, 后4位: cdef, 中间: 19-8=11个*
        let long_key = "sk-1234567890abcdef";
        let masked = super::mask_api_key(long_key);
        assert_eq!(masked, "sk-1***********cdef");

        // 测试短密钥
        let short_key = "short";
        let masked_short = super::mask_api_key(short_key);
        assert_eq!(masked_short, "*****");

        // 测试边界情况
        let boundary_key = "12345678"; // 正好8位
        let masked_boundary = super::mask_api_key(boundary_key);
        assert_eq!(masked_boundary, "********");

        // 测试另一个长密钥确保逻辑正确
        let another_key = "abcdefghijklmnop"; // 16个字符
        let masked_another = super::mask_api_key(another_key);
        assert_eq!(masked_another, "abcd********mnop"); // 前4+后4+中间8个*
    }

    #[test]
    fn test_validation() {
        use validator::Validate;

        // 测试有效的创建请求
        let valid_req = CreateModelProviderRequest {
            name: "Valid Provider".to_string(),
            api_key: "valid-api-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.openai.com".to_string(),
            icon: "".to_string(),
            models: vec![],
            enabled: true,
            description: "Valid description".to_string(),
        };
        assert!(valid_req.validate().is_ok());

        // 测试无效的API类型
        let invalid_api_type = CreateModelProviderRequest {
            name: "Test Provider".to_string(),
            api_key: "test-key".to_string(),
            api_type: "invalid-type".to_string(),
            base_url: "https://api.test.com".to_string(),
            icon: "".to_string(),
            models: vec![],
            enabled: true,
            description: "Test description".to_string(),
        };
        assert!(invalid_api_type.validate().is_err());

        // 测试无效的URL
        let invalid_url = CreateModelProviderRequest {
            name: "Test Provider".to_string(),
            api_key: "test-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "not-a-url".to_string(),
            icon: "".to_string(),
            models: vec![],
            enabled: true,
            description: "Test description".to_string(),
        };
        assert!(invalid_url.validate().is_err());
    }

    #[test]
    fn test_model_settings_validation() {
        use validator::Validate;

        // 测试有效的模型设置
        let valid_settings = ModelSettings {
            temperature: Some(0.8),
            top_p: Some(0.9),
            presence_penalty: Some(0.5),
            frequency_penalty: Some(-0.5),
            max_tokens: Some(1024),
        };
        assert!(valid_settings.validate().is_ok());

        // 测试无效的温度值
        let invalid_temp = ModelSettings {
            temperature: Some(3.0), // 超出范围
            top_p: Some(0.9),
            presence_penalty: None,
            frequency_penalty: None,
            max_tokens: Some(1024),
        };
        assert!(invalid_temp.validate().is_err());

        // 测试无效的max_tokens
        let invalid_tokens = ModelSettings {
            temperature: Some(0.8),
            top_p: Some(0.9),
            presence_penalty: None,
            frequency_penalty: None,
            max_tokens: Some(50000), // 超出范围
        };
        assert!(invalid_tokens.validate().is_err());
    }
}
