//! 数据验证模块
//!
//! 提供自定义验证函数和验证错误处理

use std::collections::HashMap;

use validator::{ValidationError, ValidationErrors};

/// 验证结果类型
pub type ValidationResult<T> = Result<T, ValidationErrors>;

/// 验证错误响应结构
#[derive(Debug, serde::Serialize)]
pub struct ValidationErrorResponse {
    pub message: String,
    pub errors: HashMap<String, Vec<String>>,
}

impl ValidationErrorResponse {
    /// 从 ValidationErrors 创建错误响应
    pub fn from_validation_errors(errors: ValidationErrors) -> Self {
        let mut error_map = HashMap::new();

        for (field, field_errors) in errors.field_errors() {
            let messages: Vec<String> = field_errors
                .iter()
                .map(|error| {
                    error
                        .message
                        .as_ref()
                        .map(|msg| msg.to_string())
                        .unwrap_or_else(|| format!("字段 {} 验证失败", field))
                })
                .collect();
            error_map.insert(field.to_string(), messages);
        }

        Self {
            message: "数据验证失败".to_string(),
            errors: error_map,
        }
    }
}

/// 自定义验证函数集合
pub mod validators {
    use super::*;

    /// 验证API类型是否有效
    pub fn validate_api_type(api_type: &str) -> Result<(), ValidationError> {
        let valid_types = [
            "openai",
            "azure",
            "anthropic",
            "google",
            "ollama",
            "custom",
            "cohere",
            "huggingface",
        ];

        if valid_types.contains(&api_type) {
            Ok(())
        } else {
            let mut error = ValidationError::new("invalid_api_type");
            error.message = Some(
                format!(
                    "不支持的API类型: {}. 支持的类型: {}",
                    api_type,
                    valid_types.join(", ")
                )
                .into(),
            );
            Err(error)
        }
    }

    /// 验证URL格式（更严格的验证）
    pub fn validate_base_url(url: &str) -> Result<(), ValidationError> {
        // 基本URL验证
        if url.is_empty() {
            let mut error = ValidationError::new("empty_url");
            error.message = Some("URL不能为空".into());
            return Err(error);
        }

        // 检查是否以http或https开头
        if !url.starts_with("http://") && !url.starts_with("https://") {
            let mut error = ValidationError::new("invalid_protocol");
            error.message = Some("URL必须以http://或https://开头".into());
            return Err(error);
        }

        // 使用url crate进行更详细的验证
        match url::Url::parse(url) {
            Ok(_) => Ok(()),
            Err(_) => {
                let mut error = ValidationError::new("invalid_url_format");
                error.message = Some("URL格式无效".into());
                Err(error)
            }
        }
    }

    /// 验证API密钥格式
    pub fn validate_api_key(api_key: &str) -> Result<(), ValidationError> {
        if api_key.is_empty() {
            let mut error = ValidationError::new("empty_api_key");
            error.message = Some("API密钥不能为空".into());
            return Err(error);
        }

        if api_key.len() < 8 {
            let mut error = ValidationError::new("api_key_too_short");
            error.message = Some("API密钥长度至少为8个字符".into());
            return Err(error);
        }

        if api_key.len() > 512 {
            let mut error = ValidationError::new("api_key_too_long");
            error.message = Some("API密钥长度不能超过512个字符".into());
            return Err(error);
        }

        Ok(())
    }

    /// 验证模型名称
    pub fn validate_model_name(name: &str) -> Result<(), ValidationError> {
        if name.is_empty() {
            let mut error = ValidationError::new("empty_model_name");
            error.message = Some("模型名称不能为空".into());
            return Err(error);
        }

        // 检查是否包含非法字符
        if name.contains(['/', '\\', ':', '*', '?', '"', '<', '>', '|']) {
            let mut error = ValidationError::new("invalid_model_name_chars");
            error.message = Some("模型名称不能包含特殊字符: / \\ : * ? \" < > |".into());
            return Err(error);
        }

        Ok(())
    }
}

/// 验证工具函数
pub mod utils {
    use super::*;

    /// 验证并返回友好的错误信息
    pub fn validate_with_friendly_errors<T: validator::Validate>(
        data: &T,
    ) -> Result<(), ValidationErrorResponse> {
        match data.validate() {
            Ok(_) => Ok(()),
            Err(errors) => Err(ValidationErrorResponse::from_validation_errors(errors)),
        }
    }

    /// 检查字符串是否为有效的UUID
    pub fn is_valid_uuid(id: &str) -> bool {
        uuid::Uuid::parse_str(id).is_ok()
    }

    /// 清理和标准化字符串
    pub fn sanitize_string(input: &str) -> String {
        input.trim().to_string()
    }

    /// 验证字符串长度范围
    pub fn validate_string_length(
        value: &str,
        min: usize,
        max: usize,
        field_name: &str,
    ) -> Result<(), ValidationError> {
        let len = value.len();
        if len < min || len > max {
            let mut error = ValidationError::new("invalid_length");
            error.message = Some(
                format!(
                    "{} 长度必须在 {}-{} 字符之间，当前长度: {}",
                    field_name, min, max, len
                )
                .into(),
            );
            Err(error)
        } else {
            Ok(())
        }
    }
}

#[cfg(test)]
mod tests {
    use super::{validators::*, *};

    #[test]
    fn test_validate_api_type() {
        assert!(validate_api_type("openai").is_ok());
        assert!(validate_api_type("azure").is_ok());
        assert!(validate_api_type("invalid").is_err());
    }

    #[test]
    fn test_validate_base_url() {
        assert!(validate_base_url("https://api.openai.com").is_ok());
        assert!(validate_base_url("http://localhost:8080").is_ok());
        assert!(validate_base_url("invalid-url").is_err());
        assert!(validate_base_url("").is_err());
    }

    #[test]
    fn test_validate_api_key() {
        assert!(validate_api_key("valid-api-key-123").is_ok());
        assert!(validate_api_key("short").is_err());
        assert!(validate_api_key("").is_err());
    }

    #[test]
    fn test_validate_model_name() {
        assert!(validate_model_name("gpt-4").is_ok());
        assert!(validate_model_name("claude-3").is_ok());
        assert!(validate_model_name("model/with/slash").is_err());
        assert!(validate_model_name("").is_err());
    }

    #[test]
    fn test_validation_error_response() {
        let mut errors = ValidationErrors::new();
        let mut error = ValidationError::new("test_error");
        error.message = Some("测试错误".into());
        errors.add("test_field", error);

        let response = ValidationErrorResponse::from_validation_errors(errors);
        assert_eq!(response.message, "数据验证失败");
        assert!(response.errors.contains_key("test_field"));
    }
}
