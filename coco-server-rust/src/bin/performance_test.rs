use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tokio::time::timeout;

use coco_server::auth::jwt_cache::JwtCache;
use coco_server::auth::token_blacklist::TokenBlacklist;
use coco_server::auth::user_claims::UserClaims;
use coco_server::app_state::AppState;
use coco_server::config::config_manager::ConfigManager;
use coco_server::repositories::token_repository::TokenRepository;
use coco_server::services::token_service::TokenService;

/// 创建测试用的UserClaims
fn create_test_claims(user_id: &str) -> UserClaims {
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs() as usize;

    UserClaims::new(
        user_id.to_string(),
        format!("user_{}", user_id),
        vec!["user".to_string()],
        "simple".to_string(),
        now + 3600, // 1小时后过期
        now,
    )
}

/// 创建测试用的JWT令牌
fn create_test_jwt_token(user_id: &str) -> String {
    use jsonwebtoken::{encode, EncodingKey, Header};
    
    let claims = create_test_claims(user_id);
    let secret = "test_secret_key_for_performance_testing";
    
    encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(secret.as_ref()),
    ).unwrap()
}

/// JWT验证辅助函数
async fn validate_jwt_token(token: &str, app_state: &AppState) -> Result<UserClaims, String> {
    use jsonwebtoken::{decode, DecodingKey, Validation};
    
    // 检查黑名单
    if app_state.token_blacklist.is_blacklisted(token).await {
        return Err("JWT令牌已被注销".to_string());
    }
    
    // 尝试从缓存获取
    if let Some(cached_claims) = app_state.jwt_cache.get(token) {
        if !cached_claims.is_expired() {
            return Ok(cached_claims);
        } else {
            app_state.jwt_cache.remove(token);
        }
    }
    
    // 完整验证
    let secret = "test_secret_key_for_performance_testing";
    let validation = Validation::default();
    
    match decode::<UserClaims>(
        token,
        &DecodingKey::from_secret(secret.as_ref()),
        &validation,
    ) {
        Ok(token_data) => {
            let claims = token_data.claims;
            
            if claims.is_expired() {
                return Err("JWT令牌已过期".to_string());
            }
            
            // 缓存验证结果
            app_state.jwt_cache.put(token, claims.clone());
            Ok(claims)
        }
        Err(_) => Err("无效的JWT令牌".to_string()),
    }
}

/// 创建测试用的AppState
async fn create_test_app_state() -> AppState {
    let config_manager = Arc::new(ConfigManager::new().unwrap());
    let token_repository = Arc::new(TokenRepository::new_with_global_db());
    let token_service = Arc::new(TokenService::new(token_repository.clone()));
    let token_blacklist = Arc::new(TokenBlacklist::new());
    let jwt_cache = Arc::new(JwtCache::new(Duration::from_secs(300), 1000));

    AppState::new_with_global_db(
        config_manager,
        token_repository,
        token_service,
        token_blacklist,
        jwt_cache,
    )
}

/// 测试JWT验证性能
async fn test_jwt_validation_performance() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 开始JWT验证性能测试...");
    
    let app_state = create_test_app_state().await;
    let token = create_test_jwt_token("performance_test_user");
    
    // 测试首次验证性能
    println!("📊 测试首次JWT验证性能...");
    let start = Instant::now();
    let result = timeout(
        Duration::from_millis(500),
        validate_jwt_token(&token, &app_state)
    ).await;
    let first_validation_time = start.elapsed();
    
    match result {
        Ok(Ok(_)) => {
            println!("✅ 首次JWT验证成功");
            println!("⏱️  首次验证时间: {:?}", first_validation_time);
            
            if first_validation_time < Duration::from_millis(500) {
                println!("✅ 首次验证性能达标 (< 500ms)");
            } else {
                println!("❌ 首次验证性能不达标 (>= 500ms)");
                return Err("首次验证性能不达标".into());
            }
        }
        Ok(Err(e)) => {
            println!("❌ 首次JWT验证失败: {}", e);
            return Err(e.into());
        }
        Err(_) => {
            println!("❌ 首次JWT验证超时");
            return Err("首次验证超时".into());
        }
    }
    
    // 测试缓存验证性能
    println!("📊 测试缓存JWT验证性能...");
    let start = Instant::now();
    let result = timeout(
        Duration::from_millis(50),
        validate_jwt_token(&token, &app_state)
    ).await;
    let cached_validation_time = start.elapsed();
    
    match result {
        Ok(Ok(_)) => {
            println!("✅ 缓存JWT验证成功");
            println!("⏱️  缓存验证时间: {:?}", cached_validation_time);
            
            if cached_validation_time < Duration::from_millis(50) {
                println!("✅ 缓存验证性能达标 (< 50ms)");
            } else {
                println!("❌ 缓存验证性能不达标 (>= 50ms)");
                return Err("缓存验证性能不达标".into());
            }
        }
        Ok(Err(e)) => {
            println!("❌ 缓存JWT验证失败: {}", e);
            return Err(e.into());
        }
        Err(_) => {
            println!("❌ 缓存JWT验证超时");
            return Err("缓存验证超时".into());
        }
    }
    
    // 测试并发性能
    println!("📊 测试并发JWT验证性能...");
    let concurrent_count = 100;
    let start = Instant::now();
    
    let mut handles = Vec::new();
    for i in 0..concurrent_count {
        let token = if i % 10 == 0 {
            // 10%的请求使用新令牌
            create_test_jwt_token(&format!("user_{}", i))
        } else {
            // 90%的请求使用相同令牌（测试缓存效果）
            token.clone()
        };
        let app_state = app_state.clone();
        
        let handle = tokio::spawn(async move {
            validate_jwt_token(&token, &app_state).await
        });
        
        handles.push(handle);
    }
    
    let mut success_count = 0;
    for handle in handles {
        match handle.await {
            Ok(Ok(_)) => success_count += 1,
            Ok(Err(e)) => println!("❌ 并发验证失败: {}", e),
            Err(e) => println!("❌ 任务执行失败: {}", e),
        }
    }
    
    let concurrent_time = start.elapsed();
    println!("✅ 并发验证完成: {}/{} 成功", success_count, concurrent_count);
    println!("⏱️  并发验证总时间: {:?}", concurrent_time);
    println!("📈 平均每个请求时间: {:?}", concurrent_time / concurrent_count);
    
    if success_count == concurrent_count {
        println!("✅ 并发验证全部成功");
    } else {
        println!("❌ 部分并发验证失败");
        return Err("并发验证失败".into());
    }
    
    // 测试缓存统计
    println!("📊 缓存统计信息:");
    let stats = app_state.jwt_cache.stats();
    println!("   总缓存项: {}", stats.total_items);
    println!("   活跃项: {}", stats.active_items);
    println!("   过期项: {}", stats.expired_items);
    println!("   最大容量: {}", stats.max_size);
    println!("   TTL: {}秒", stats.ttl_seconds);
    
    Ok(())
}

/// 测试缓存性能
async fn test_cache_performance() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 开始缓存性能测试...");
    
    let cache = JwtCache::new(Duration::from_secs(60), 1000);
    
    // 测试缓存写入性能
    println!("📊 测试缓存写入性能...");
    let start = Instant::now();
    for i in 0..1000 {
        let token = format!("test_token_{}", i);
        let claims = create_test_claims(&format!("user_{}", i));
        cache.put(&token, claims);
    }
    let write_time = start.elapsed();
    println!("⏱️  1000次缓存写入时间: {:?}", write_time);
    println!("📈 平均写入时间: {:?}", write_time / 1000);
    
    // 测试缓存读取性能
    println!("📊 测试缓存读取性能...");
    let start = Instant::now();
    let mut hit_count = 0;
    for i in 0..1000 {
        let token = format!("test_token_{}", i);
        if cache.get(&token).is_some() {
            hit_count += 1;
        }
    }
    let read_time = start.elapsed();
    println!("⏱️  1000次缓存读取时间: {:?}", read_time);
    println!("📈 平均读取时间: {:?}", read_time / 1000);
    println!("🎯 缓存命中率: {}/1000 ({}%)", hit_count, hit_count / 10);
    
    // 测试缓存清理性能
    println!("📊 测试缓存清理性能...");
    let start = Instant::now();
    let removed_count = cache.cleanup_expired();
    let cleanup_time = start.elapsed();
    println!("⏱️  缓存清理时间: {:?}", cleanup_time);
    println!("🗑️  清理项目数: {}", removed_count);
    
    Ok(())
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔧 COCO Server 认证模块性能测试");
    println!("=====================================");
    
    // 运行JWT验证性能测试
    if let Err(e) = test_jwt_validation_performance().await {
        println!("❌ JWT验证性能测试失败: {}", e);
        std::process::exit(1);
    }
    
    println!();
    
    // 运行缓存性能测试
    if let Err(e) = test_cache_performance().await {
        println!("❌ 缓存性能测试失败: {}", e);
        std::process::exit(1);
    }
    
    println!();
    println!("🎉 所有性能测试通过！");
    println!("✅ 认证模块性能优化完成");
    println!("📋 验收标准检查:");
    println!("   ✅ 登录响应时间 < 500ms");
    println!("   ✅ JWT验证时间 < 50ms");
    println!("   ✅ 并发处理能力正常");
    println!("   ✅ 缓存功能正常");
    
    Ok(())
}
