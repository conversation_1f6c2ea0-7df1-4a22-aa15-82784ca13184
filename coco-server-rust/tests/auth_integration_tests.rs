use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::time::timeout;

use coco_server::app_state::AppState;
use coco_server::auth::jwt_cache::JwtCache;
use coco_server::auth::token_blacklist::TokenBlacklist;
use coco_server::auth::user_claims::UserClaims;
use coco_server::config::config_manager::ConfigManager;
use coco_server::repositories::token_repository::TokenRepository;
use coco_server::services::token_service::TokenService;

/// 创建测试用的AppState
async fn create_test_app_state() -> AppState {
    let config_manager = Arc::new(ConfigManager::new().unwrap());
    let token_repository = Arc::new(TokenRepository::new_with_global_db());
    let token_service = Arc::new(TokenService::new(token_repository.clone()));
    let token_blacklist = Arc::new(TokenBlacklist::new());
    let jwt_cache = Arc::new(JwtCache::new(Duration::from_secs(300), 1000));

    AppState::new_with_global_db(
        config_manager,
        token_repository,
        token_service,
        token_blacklist,
        jwt_cache,
    )
}

/// 创建测试用的UserClaims
fn create_test_claims(user_id: &str) -> UserClaims {
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs() as usize;

    UserClaims::new(
        user_id.to_string(),
        format!("user_{}", user_id),
        vec!["user".to_string()],
        "simple".to_string(),
        now + 3600, // 1小时后过期
        now,
    )
}

/// 创建测试用的JWT令牌
fn create_test_jwt_token(user_id: &str) -> String {
    use jsonwebtoken::{encode, EncodingKey, Header};

    let claims = create_test_claims(user_id);
    let secret = std::env::var("JWT_SECRET").unwrap_or_else(|_| "test_secret_key".to_string());

    encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(secret.as_ref()),
    )
    .unwrap()
}

/// 模拟JWT验证逻辑
async fn validate_jwt_token(token: &str, app_state: &AppState) -> Result<UserClaims, String> {
    use jsonwebtoken::{decode, DecodingKey, Validation};

    // 检查黑名单
    if app_state.token_blacklist.is_blacklisted(token).await {
        return Err("JWT令牌已被注销".to_string());
    }

    // 尝试从缓存获取
    if let Some(cached_claims) = app_state.jwt_cache.get(token) {
        if !cached_claims.is_expired() {
            return Ok(cached_claims);
        } else {
            app_state.jwt_cache.remove(token);
        }
    }

    // 完整验证
    let secret = std::env::var("JWT_SECRET").unwrap_or_else(|_| "test_secret_key".to_string());
    let validation = Validation::default();

    match decode::<UserClaims>(
        token,
        &DecodingKey::from_secret(secret.as_ref()),
        &validation,
    ) {
        Ok(token_data) => {
            let claims = token_data.claims;

            if claims.is_expired() {
                return Err("JWT令牌已过期".to_string());
            }

            // 缓存验证结果
            app_state.jwt_cache.put(token, claims.clone());
            Ok(claims)
        }
        Err(e) => {
            eprintln!("JWT验证失败: {}", e);
            Err("无效的JWT令牌".to_string())
        }
    }
}

#[tokio::test]
async fn test_complete_authentication_flow() {
    let app_state = create_test_app_state().await;
    let user_id = "test_user_123";
    let token = create_test_jwt_token(user_id);

    // 第一次验证 - 应该成功并缓存结果
    let result1 = validate_jwt_token(&token, &app_state).await;
    assert!(result1.is_ok());
    let claims1 = result1.unwrap();
    assert_eq!(claims1.user_id, user_id);

    // 第二次验证 - 应该从缓存获取
    let result2 = validate_jwt_token(&token, &app_state).await;
    assert!(result2.is_ok());
    let claims2 = result2.unwrap();
    assert_eq!(claims2.user_id, user_id);

    // 验证缓存统计
    let stats = app_state.jwt_cache.stats();
    assert_eq!(stats.total_items, 1);
}

#[tokio::test]
async fn test_jwt_blacklist_functionality() {
    let app_state = create_test_app_state().await;
    let user_id = "test_user_blacklist";
    let token = create_test_jwt_token(user_id);

    // 首次验证应该成功
    let result1 = validate_jwt_token(&token, &app_state).await;
    assert!(result1.is_ok());

    // 将令牌加入黑名单
    app_state.token_blacklist.add_token(&token).await;

    // 再次验证应该失败
    let result2 = validate_jwt_token(&token, &app_state).await;
    assert!(result2.is_err());
    assert_eq!(result2.unwrap_err(), "JWT令牌已被注销");
}

#[tokio::test]
async fn test_jwt_cache_expiration() {
    let jwt_cache = Arc::new(JwtCache::new(Duration::from_millis(100), 100));
    let token_repository = Arc::new(TokenRepository::new_with_global_db());
    let app_state = AppState::new_with_global_db(
        Arc::new(ConfigManager::new().unwrap()),
        token_repository.clone(),
        Arc::new(TokenService::new(token_repository)),
        Arc::new(TokenBlacklist::new()),
        jwt_cache,
    );

    let user_id = "test_user_expiry";
    let token = create_test_jwt_token(user_id);

    // 首次验证并缓存
    let result1 = validate_jwt_token(&token, &app_state).await;
    assert!(result1.is_ok());

    // 立即再次验证，应该从缓存获取
    let result2 = validate_jwt_token(&token, &app_state).await;
    assert!(result2.is_ok());

    // 等待缓存过期
    tokio::time::sleep(Duration::from_millis(150)).await;

    // 验证缓存已过期，需要重新验证
    let result3 = validate_jwt_token(&token, &app_state).await;
    assert!(result3.is_ok());
}

#[tokio::test]
async fn test_concurrent_jwt_validation() {
    let app_state = Arc::new(create_test_app_state().await);
    let user_id = "test_user_concurrent";
    let token = create_test_jwt_token(user_id);

    // 创建多个并发验证任务
    let mut handles = Vec::new();
    for i in 0..50 {
        let token = token.clone();
        let app_state = app_state.clone();

        let handle = tokio::spawn(async move {
            let result = validate_jwt_token(&token, &app_state).await;
            (i, result)
        });

        handles.push(handle);
    }

    // 等待所有任务完成
    let mut success_count = 0;
    for handle in handles {
        let (task_id, result) = handle.await.unwrap();
        match result {
            Ok(claims) => {
                assert_eq!(claims.user_id, user_id);
                success_count += 1;
            }
            Err(e) => {
                eprintln!("任务 {} 失败: {}", task_id, e);
            }
        }
    }

    // 所有验证都应该成功
    assert_eq!(success_count, 50);

    // 验证缓存中只有一个项目（因为是同一个令牌）
    let stats = app_state.jwt_cache.stats();
    assert_eq!(stats.total_items, 1);
}

#[tokio::test]
async fn test_invalid_jwt_handling() {
    let app_state = create_test_app_state().await;

    // 测试完全无效的令牌
    let invalid_token = "invalid.jwt.token";
    let result1 = validate_jwt_token(invalid_token, &app_state).await;
    assert!(result1.is_err());

    // 测试格式正确但签名无效的令牌
    let malformed_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.invalid_signature";
    let result2 = validate_jwt_token(malformed_token, &app_state).await;
    assert!(result2.is_err());

    // 验证缓存中没有无效令牌
    let stats = app_state.jwt_cache.stats();
    assert_eq!(stats.total_items, 0);
}

#[tokio::test]
async fn test_jwt_performance_requirements() {
    let app_state = create_test_app_state().await;
    let user_id = "test_user_performance";
    let token = create_test_jwt_token(user_id);

    // 测试首次验证性能（应该 < 500ms）
    let start = std::time::Instant::now();
    let result1 = timeout(
        Duration::from_millis(500),
        validate_jwt_token(&token, &app_state),
    )
    .await;
    let first_validation_time = start.elapsed();

    assert!(result1.is_ok(), "首次JWT验证超时");
    assert!(result1.unwrap().is_ok(), "首次JWT验证失败");
    println!("首次JWT验证时间: {:?}", first_validation_time);

    // 测试缓存验证性能（应该 < 50ms）
    let start = std::time::Instant::now();
    let result2 = timeout(
        Duration::from_millis(50),
        validate_jwt_token(&token, &app_state),
    )
    .await;
    let cached_validation_time = start.elapsed();

    assert!(result2.is_ok(), "缓存JWT验证超时");
    assert!(result2.unwrap().is_ok(), "缓存JWT验证失败");
    println!("缓存JWT验证时间: {:?}", cached_validation_time);

    // 验证性能要求
    assert!(
        first_validation_time < Duration::from_millis(500),
        "首次JWT验证时间超过500ms: {:?}",
        first_validation_time
    );
    assert!(
        cached_validation_time < Duration::from_millis(50),
        "缓存JWT验证时间超过50ms: {:?}",
        cached_validation_time
    );
}

#[tokio::test]
async fn test_cache_size_limits() {
    let jwt_cache = Arc::new(JwtCache::new(Duration::from_secs(300), 5)); // 限制为5个项目
    let token_repository = Arc::new(TokenRepository::new_with_global_db());
    let app_state = AppState::new_with_global_db(
        Arc::new(ConfigManager::new().unwrap()),
        token_repository.clone(),
        Arc::new(TokenService::new(token_repository)),
        Arc::new(TokenBlacklist::new()),
        jwt_cache,
    );

    // 添加6个不同的令牌，应该只保留5个
    for i in 0..6 {
        let user_id = format!("test_user_{}", i);
        let token = create_test_jwt_token(&user_id);
        let _ = validate_jwt_token(&token, &app_state).await;
    }

    // 验证缓存大小限制
    let stats = app_state.jwt_cache.stats();
    assert_eq!(stats.total_items, 5, "缓存大小应该限制为5个项目");
}

#[tokio::test]
async fn test_cache_cleanup() {
    let jwt_cache = Arc::new(JwtCache::new(Duration::from_millis(50), 100));
    let token_repository = Arc::new(TokenRepository::new_with_global_db());
    let app_state = AppState::new_with_global_db(
        Arc::new(ConfigManager::new().unwrap()),
        token_repository.clone(),
        Arc::new(TokenService::new(token_repository)),
        Arc::new(TokenBlacklist::new()),
        jwt_cache.clone(),
    );

    // 添加一些令牌到缓存
    for i in 0..10 {
        let user_id = format!("test_user_{}", i);
        let token = create_test_jwt_token(&user_id);
        let _ = validate_jwt_token(&token, &app_state).await;
    }

    // 验证缓存中有项目
    let stats_before = app_state.jwt_cache.stats();
    assert_eq!(stats_before.total_items, 10);

    // 等待缓存过期
    tokio::time::sleep(Duration::from_millis(100)).await;

    // 手动清理过期项
    let removed_count = app_state.jwt_cache.cleanup_expired();
    assert_eq!(removed_count, 10, "应该清理所有过期项");

    // 验证缓存已清空
    let stats_after = app_state.jwt_cache.stats();
    assert_eq!(stats_after.total_items, 0);
}
