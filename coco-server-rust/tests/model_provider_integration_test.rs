use axum::{
    body::Body,
    http::{Request, StatusCode},
    Router,
};
use serde_json::json;
use tower::ServiceExt;

use coco_server::{
    config::config_manager::ConfigManager,
    handlers::model_provider_handler::{create_model_provider_handler, get_model_provider_handler},
};

/// 创建测试路由器
fn create_test_router() -> Router {
    let config_manager = std::sync::Arc::new(
        ConfigManager::new().expect("Failed to create config manager")
    );

    Router::new()
        .route("/model_provider/", axum::routing::post(create_model_provider_handler))
        .route("/model_provider/:id", axum::routing::get(get_model_provider_handler))
        .with_state(config_manager)
}

#[tokio::test]
async fn test_create_model_provider_route_exists() {
    let app = create_test_router();

    let request_body = json!({
        "name": "Test Provider",
        "api_key": "test-key",
        "api_type": "openai",
        "base_url": "https://api.test.com",
        "icon": "test-icon",
        "models": [],
        "enabled": true,
        "description": "Test description"
    });

    let request = Request::builder()
        .method("POST")
        .uri("/model_provider/")
        .header("content-type", "application/json")
        .body(Body::from(request_body.to_string()))
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 我们期望得到一个错误响应，因为数据库没有初始化
    // 但路由应该存在并且能够处理请求
    assert!(response.status() == StatusCode::INTERNAL_SERVER_ERROR || 
            response.status() == StatusCode::BAD_REQUEST ||
            response.status() == StatusCode::CREATED);
}

#[tokio::test]
async fn test_get_model_provider_route_exists() {
    let app = create_test_router();

    let request = Request::builder()
        .method("GET")
        .uri("/model_provider/test-id")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    // 我们期望得到一个错误响应，因为数据库没有初始化
    // 但路由应该存在并且能够处理请求
    assert!(response.status() == StatusCode::INTERNAL_SERVER_ERROR || 
            response.status() == StatusCode::NOT_FOUND);
}

#[tokio::test]
async fn test_invalid_route_returns_404() {
    let app = create_test_router();

    let request = Request::builder()
        .method("GET")
        .uri("/invalid_route")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();

    assert_eq!(response.status(), StatusCode::NOT_FOUND);
}
